import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:image_network/image_network.dart';
import 'package:get/get.dart';
import '../services/post_service.dart';
import '../managers/wallet_manager.dart';
import '../controllers/profile_controller.dart';

class ProfileScreen extends StatefulWidget {
  final String userId;
  const ProfileScreen({super.key, required this.userId});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final ProfileController _profileController = Get.find<ProfileController>();
  final WalletManager _walletManager = WalletManager();
  final PostService _postService = PostService();

  @override
  void initState() {
    super.initState();
    _initializeWallet();
    _loadUserProfile();
  }

  Future<void> _initializeWallet() async {
    try {
      if (!_walletManager.isInitialized) {
        await _walletManager.initialize();
      }
      // Listen to wallet state changes for real-time balance updates
      _walletManager.addListener(_onWalletStateChanged);
    } catch (e) {
      debugPrint('Error initializing wallet in profile screen: $e');
    }
  }

  void _onWalletStateChanged() {
    if (mounted) {
      setState(() {
        // Trigger rebuild when wallet state changes
      });
    }
  }

  Future<void> _loadUserProfile() async {
    // If viewing another user's profile, load their data
    if (widget.userId != _profileController.currentUserId) {
      await _profileController.getUserProfile(widget.userId);
    }
  }

  @override
  void dispose() {
    _walletManager.removeListener(_onWalletStateChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentUid = FirebaseAuth.instance.currentUser?.uid;
    final bool isSelf = currentUid == widget.userId;

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: false,
        titleSpacing: 0,
        centerTitle: true,
        title: Row(
          children: [
            const Spacer(),
            // Paid Post label – show if user has paid posts
            FutureBuilder<List<Post>>(
              future: Future.value(
                _postService
                    .getAllPosts()
                    .where((p) => p.authorId == widget.userId && p.price > 0)
                    .toList(),
              ),
              builder: (context, postSnap) {
                if (!postSnap.hasData || postSnap.data!.isEmpty) {
                  return const SizedBox();
                }
                final topPrice = postSnap.data!
                    .map((e) => e.price)
                    .reduce((a, b) => a > b ? a : b);
                return Row(
                  children: [
                    Text(
                      'Paid Post',
                      style: TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.w600,
                        decoration: TextDecoration.underline,
                        decorationThickness: 1,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: Colors.grey.shade400),
                      ),
                      child: Text(
                        '\$ ${topPrice.toStringAsFixed(0)}',
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(width: 16),
          ],
        ),
        actions: isSelf
            ? [
                IconButton(
                  icon: const Icon(Icons.edit, color: Colors.black),
                  onPressed: () {
                    Navigator.pushNamed(context, '/edit_profile');
                  },
                ),
              ]
            : null,
      ),
      body: Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: kIsWeb ? 800 : double.infinity),
          child: _buildProfileContent(isSelf),
        ),
      ),
    );
  }

  Widget _buildProfileContent(bool isSelf) {
    // If viewing current user, use reactive data from ProfileController
    if (isSelf) {
      return Obx(() {
        if (_profileController.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final username = _profileController.username.isNotEmpty
            ? _profileController.username
            : _profileController.displayName;
        final bio = _profileController.bio;
        final profileImageUrl = _profileController.profileImageUrl;

        final userPosts = _postService
            .getAllPosts()
            .where((p) => p.authorId == widget.userId)
            .toList();

        return _buildProfileUI(
          username: username,
          bio: bio,
          profileImageUrl: profileImageUrl,
          userPosts: userPosts,
          isSelf: isSelf,
        );
      });
    } else {
      // For other users, use FutureBuilder with cached data
      return FutureBuilder<Map<String, dynamic>?>(
        future: _profileController.getUserProfile(widget.userId),
        builder: (context, snap) {
          if (snap.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (!snap.hasData || snap.data == null) {
            return const Center(child: Text('No Data Found'));
          }

          final data = snap.data!;
          final username = data['username'] ?? data['displayName'] ?? 'Unknown';
          final bio = data['bio'] ?? '';
          final profileImageUrl = data['profileImageUrl'];

          final userPosts = _postService
              .getAllPosts()
              .where((p) => p.authorId == widget.userId)
              .toList();

          return _buildProfileUI(
            username: username,
            bio: bio,
            profileImageUrl: profileImageUrl,
            userPosts: userPosts,
            isSelf: isSelf,
          );
        },
      );
    }
  }

  Widget _buildProfileUI({
    required String username,
    required String bio,
    required String profileImageUrl,
    required List<Post> userPosts,
    required bool isSelf,
  }) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with cover image and profile avatar
          // Profile image positioned at top
          Align(
            alignment: Alignment.centerLeft,
            child: Padding(
              padding: const EdgeInsets.all(0),
              child: Stack(
                alignment: Alignment.bottomLeft,
                children: [
                  Center(
                    child: CircleAvatar(
                      radius: 60,
                      backgroundColor: Colors.grey.shade300,
                      child: ClipOval(
                        child: ImageNetwork(
                          image:
                              profileImageUrl.isNotEmpty ? profileImageUrl : '',
                          height: 120,
                          width: 120,
                          duration: 100,
                          curve: Curves.easeIn,
                          onPointer: true,
                          debugPrint: false,
                          backgroundColor: Colors.white,
                          fitAndroidIos: BoxFit.cover,
                          fitWeb: BoxFitWeb.cover,
                          borderRadius: BorderRadius.circular(70),
                          onLoading: CircularProgressIndicator(
                            color: Colors.indigoAccent,
                            strokeWidth: 0.1,
                          ),
                          onError: const Icon(Icons.person, color: Colors.blue),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              username.isNotEmpty ? username : 'Unknown',
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
          ),
          if (bio.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              child: Text(bio, style: const TextStyle(color: Colors.grey)),
            ),
          const SizedBox(height: 16),

          // Fund Account Section (self only)
          if (isSelf) ...[
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'Fund Account',
                style: TextStyle(
                  color: Colors.grey[800],
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: _AddPaymentButton(
                onPressed: () {
                  Navigator.pushNamed(context, '/wallet');
                },
              ),
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: _buildWalletBalance(),
            ),
            const SizedBox(height: 24),
          ],

          // Follow other accounts section (only for self)
          if (isSelf) ...[
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: const [
                        Text(
                          'Follow Other accounts',
                          style: TextStyle(fontWeight: FontWeight.w600),
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 2),
                        Text(
                          'Connect with other users around the world.',
                          style: TextStyle(color: Colors.grey, fontSize: 12),
                          overflow: TextOverflow.visible,
                        ),
                      ],
                    ),
                  ),
                  // IconButton(icon: const Icon(Icons.close), onPressed: () {}),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).pushNamed('/connect');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF5159FF),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  minimumSize: const Size(double.infinity, 48),
                ),
                icon: const Icon(Icons.people),
                label: const Text('Connect'),
              ),
            ),
            const SizedBox(height: 16),
          ],
        ],
      ),
    );
  }

  Widget _buildWalletBalance() {
    return ListenableBuilder(
      listenable: _walletManager,
      builder: (context, _) {
        final balance = _walletManager.balance;
        final pct = (balance / 100).clamp(0.0, 1.0);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Stack(
              children: [
                Container(
                  height: 2,
                  decoration: BoxDecoration(color: Colors.grey.shade400),
                ),
                FractionallySizedBox(
                  widthFactor: pct,
                  child: Container(height: 2, color: const Color(0xFF5159FF)),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(_walletManager.formatCurrency(balance)),
          ],
        );
      },
    );
  }
}

class _AddPaymentButton extends StatelessWidget {
  final VoidCallback onPressed;
  const _AddPaymentButton({required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color(0xFF5159FF),
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
      ),
      icon: const Icon(Icons.add),
      label: const Text('ReUp'),
    );
  }
}
