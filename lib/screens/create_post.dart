import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:get/get.dart';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:money_mouthy_two/controllers/category_controller.dart';
import '../controllers/wallet_controller.dart';
import '../controllers/post_controller.dart';
import '../controllers/draft_controller.dart';
import '../models/draft_model.dart';
import 'dart:io';
import '../services/post_service.dart';
import '../services/post_amount_service.dart';
import '../services/video_thumbnail_service.dart';
// import '../screens/media_editor_screen.dart';
import 'wallet_screen.dart';
import '../widgets/home/<USER>';
import '../middleware/user_status_middleware.dart';

class CreatePostScreen extends StatefulWidget {
  const CreatePostScreen({super.key});

  @override
  State<CreatePostScreen> createState() => _CreatePostScreenState();
}

class _CreatePostScreenState extends State<CreatePostScreen> {
  final _contentController = TextEditingController();
  late final WalletController walletController;
  late final PostController _postController;
  late final DraftController _draftController;
  final PostAmountService _postAmountService = PostAmountService();
  final CategoryController _categoryController = Get.find<CategoryController>();

  double _postPrice = 0.05; // Default minimum, will be updated from Firestore
  bool _isLoading = false;
  bool _isSubmitting = false;

  // Media upload variables
  final List<XFile> _selectedImages = [];
  final List<XFile> _selectedVideos = [];
  final List<XFile> _selectedVideoAds = [];
  String? _linkUrl;
  final ImagePicker _imagePicker = ImagePicker();

  // Poll variables
  bool _hasPoll = false;

  // Character limit
  static const int _maxCharacters = 480;

  // Categories are now managed by the standardized Categories class
  // This getter provides backward compatibility
  List<Map<String, dynamic>> get _categories => Categories.legacyFormat;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _loadAutoDraft();

    // Listen to post amount changes from drawer
    _postAmountService.postAmountStream.listen((amount) {
      if (mounted && _postPrice != amount) {
        setState(() {
          _postPrice = amount;
        });
      }
    });
  }

  Future<void> _initializeServices() async {
    try {
      // Initialize controllers (they should be available since we're in MainNavigationScreen)
      walletController = Get.find<WalletController>();
      _postController = Get.find<PostController>();
      _draftController = Get.find<DraftController>();

      await walletController.initialize();
      await _loadSavedPostAmount();
      if (mounted) {
        setState(() {
          // Refresh UI after services are ready
        });
      }
    } catch (e) {
      debugPrint('Error initializing services in CreatePostScreen: $e');
    }
  }

  Future<void> _loadSavedPostAmount() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    try {
      final doc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();

      if (doc.exists && doc.data()!.containsKey('postAmount')) {
        final savedAmount = (doc.data()!['postAmount'] as num).toDouble();
        if (mounted) {
          setState(() {
            _postPrice = savedAmount;
          });
          // Update the shared service
          _postAmountService.updatePostAmount(savedAmount);
          // debugPrint(
          //   'Loaded saved post amount: \$${savedAmount.toStringAsFixed(2)}',
          // );
        }
      }
    } catch (e) {
      debugPrint('Failed to load saved post amount: $e');
      // Keep default value if loading fails
    }
  }

  /// Load auto-draft if available
  Future<void> _loadAutoDraft() async {
    try {
      await _draftController.loadAutoDraft();
      final autoDraft = _draftController.autoDraft;
      if (autoDraft != null && mounted) {
        setState(() {
          _contentController.text = autoDraft.content;
          _postPrice = autoDraft.price;
          _linkUrl = autoDraft.linkUrl;
          _hasPoll = autoDraft.hasPoll;
          // Note: Media files would need special handling for XFile conversion
        });
      }
    } catch (e) {
      debugPrint('Error loading auto-draft: $e');
    }
  }

  /// Save current content as draft
  Future<void> _saveDraft() async {
    try {
      final success = await _draftController.saveDraft(
        content: _contentController.text,
        price: _postPrice,
        category: _categoryController.selectedCategoryName,
        isPublic: true,
        allowComments: true,
        linkUrl: _linkUrl,
        images: _selectedImages,
        videos: _selectedVideos,
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Draft saved successfully'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error saving draft: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to save draft'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  /// Show draft management dialog
  void _showDraftDialog() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Drafts',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Drafts list
              Expanded(
                child: Obx(() {
                  if (_draftController.isLoading) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (!_draftController.hasDrafts) {
                    return const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.drafts, size: 64, color: Colors.grey),
                          SizedBox(height: 16),
                          Text(
                            'No drafts saved',
                            style: TextStyle(fontSize: 16, color: Colors.grey),
                          ),
                        ],
                      ),
                    );
                  }

                  return ListView.builder(
                    itemCount: _draftController.drafts.length,
                    itemBuilder: (context, index) {
                      final draft = _draftController.drafts[index];
                      return _buildDraftItem(draft);
                    },
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build individual draft item
  Widget _buildDraftItem(DraftModel draft) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        title: Text(
          draft.contentPreview,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Category: ${draft.category}'),
            Text('Price: \$${draft.price.toStringAsFixed(2)}'),
            Text(draft.formattedCreatedAt),
            if (draft.hasMedia)
              Text(
                '${draft.imagePaths.length} images, ${draft.videoPaths.length} videos',
                style: const TextStyle(color: Colors.blue),
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () => _loadDraft(draft),
              icon: const Icon(Icons.edit),
              tooltip: 'Load Draft',
            ),
            IconButton(
              onPressed: () => _deleteDraft(draft.id),
              icon: const Icon(Icons.delete, color: Colors.red),
              tooltip: 'Delete Draft',
            ),
          ],
        ),
      ),
    );
  }

  /// Load a draft into the editor
  void _loadDraft(DraftModel draft) {
    setState(() {
      _contentController.text = draft.content;
      _postPrice = draft.price;
      _linkUrl = draft.linkUrl;
      _hasPoll = draft.hasPoll;
      // Note: Loading media files would require converting paths back to XFile
    });
    Navigator.pop(context); // Close dialog

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Draft loaded'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// Delete a draft
  Future<void> _deleteDraft(String draftId) async {
    final success = await _draftController.deleteDraft(draftId);
    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Draft deleted'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  @override
  void dispose() {
    _draftController.stopAutoSave();
    _contentController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImages.add(image);
        });
      }
    } catch (e) {
      _showErrorMessage('Failed to pick image: $e');
    }
  }

  Future<void> _takePhoto() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImages.add(image);
        });
      }
    } catch (e) {
      _showErrorMessage('Failed to take photo: $e');
    }
  }

  Future<void> _pickVideo() async {
    try {
      // Check if already have a video
      if (_selectedVideos.isNotEmpty) {
        _showErrorMessage('You can only add 1 video per post');
        return;
      }

      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(
          seconds: 30,
        ), // 30 second limit for regular videos
      );

      if (video != null) {
        // Validate video duration
        final isValid = await _validateVideoDuration(video, 30);
        if (isValid) {
          setState(() {
            _selectedVideos.add(video);
          });
        }
      }
    } catch (e) {
      _showErrorMessage('Failed to pick video: $e');
    }
  }

  Future<void> _recordVideo() async {
    try {
      // Check if already have a video
      if (_selectedVideos.isNotEmpty) {
        _showErrorMessage('You can only add 1 video per post');
        return;
      }

      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.camera,
        maxDuration: const Duration(
          seconds: 30,
        ), // 30 second limit for regular videos
      );

      if (video != null) {
        // Validate video duration
        final isValid = await _validateVideoDuration(video, 30);
        if (isValid) {
          setState(() {
            _selectedVideos.add(video);
          });
        }
      }
    } catch (e) {
      _showErrorMessage('Failed to record video: $e');
    }
  }

  Future<void> _pickVideoAd() async {
    try {
      // Check if already have a video ad
      if (_selectedVideoAds.isNotEmpty) {
        _showErrorMessage('You can only add 1 video ad per post');
        return;
      }

      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(
          seconds: 60,
        ), // 60 second limit for video ads
      );

      if (video != null) {
        // Validate video duration
        final isValid = await _validateVideoDuration(video, 60);
        if (isValid) {
          setState(() {
            _selectedVideoAds.add(video);
            // Update price when video ad is selected
            _updatePriceForVideoAd();
          });
        }
      }
    } catch (e) {
      _showErrorMessage('Failed to pick video ad: $e');
    }
  }

  Future<void> _recordVideoAd() async {
    try {
      // Check if already have a video ad
      if (_selectedVideoAds.isNotEmpty) {
        _showErrorMessage('You can only add 1 video ad per post');
        return;
      }

      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.camera,
        maxDuration: const Duration(
          seconds: 60,
        ), // 60 second limit for video ads
      );

      if (video != null) {
        // Validate video duration
        final isValid = await _validateVideoDuration(video, 60);
        if (isValid) {
          setState(() {
            _selectedVideoAds.add(video);
            // Update price when video ad is selected
            _updatePriceForVideoAd();
          });
        }
      }
    } catch (e) {
      _showErrorMessage('Failed to record video ad: $e');
    }
  }

  /// Update price when video ad is selected
  void _updatePriceForVideoAd() {
    final selectedCategory = _categoryController.selectedCategoryName;
    final topPost = _postController.getTopPaidPostForCategory(selectedCategory);

    if (topPost != null) {
      final topPostPrice = topPost.price;
      final newPrice = topPostPrice + 1.0; // Top paid post price + $1
      setState(() {
        _postPrice = newPrice;
      });
      // Update the shared service
      _postAmountService.updatePostAmount(newPrice);
      debugPrint(
          'Video Ad selected: Updated price to \$${newPrice.toStringAsFixed(2)} (Top post: \$${topPostPrice.toStringAsFixed(2)} + \$1.00)');
    } else {
      // If no top post exists, use default minimum + $1
      final newPrice = 0.05 + 1.0;
      setState(() {
        _postPrice = newPrice;
      });
      _postAmountService.updatePostAmount(newPrice);
      debugPrint(
          'Video Ad selected: No top post found, using default + \$1.00 = \$${newPrice.toStringAsFixed(2)}');
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  void _removeVideo(int index) {
    setState(() {
      _selectedVideos.removeAt(index);
    });
  }

  void _removeVideoAd(int index) {
    setState(() {
      _selectedVideoAds.removeAt(index);
      // Reset price to saved amount when video ad is removed
      _loadSavedPostAmount();
    });
  }

  /// Validate video duration
  Future<bool> _validateVideoDuration(XFile videoFile, int maxSeconds) async {
    try {
      final duration = await VideoThumbnailService.getVideoDurationFromXFile(
        videoFile,
      );

      if (duration == null) {
        // If we can't get duration (e.g., on web), allow the video
        debugPrint('Could not determine video duration, allowing video');
        return true;
      }

      if (duration.inSeconds > maxSeconds) {
        final minutes = maxSeconds ~/ 60;
        final seconds = maxSeconds % 60;
        final timeLimit =
            minutes > 0 ? '${minutes}m ${seconds}s' : '${seconds}s';

        _showErrorMessage(
          'Video is too long. Maximum duration is $timeLimit. '
          'Your video is ${VideoThumbnailService.formatDuration(duration)}.',
        );
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('Error validating video duration: $e');
      // If validation fails, allow the video to avoid blocking users
      return true;
    }
  }

  // void _editMedia(XFile mediaFile, bool isVideo, int index) {
  //   Navigator.push(
  //     context,
  //     MaterialPageRoute(
  //       builder:
  //           (context) => MediaEditorScreen(
  //             mediaFile: mediaFile,
  //             isVideo: isVideo,
  //             onSave: (editedFile) {
  //               setState(() {
  //                 if (isVideo) {
  //                   _selectedVideos[index] = editedFile;
  //                 } else {
  //                   _selectedImages[index] = editedFile;
  //                 }
  //               });
  //             },
  //           ),
  //     ),
  //   );
  // }

  void _showAddLinkDialog() {
    final TextEditingController linkController = TextEditingController(
      text: _linkUrl,
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Link'),
        content: TextField(
          controller: linkController,
          decoration: const InputDecoration(
            hintText: 'Enter URL (https://...)',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.url,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final url = linkController.text.trim();
              if (url.isNotEmpty) {
                setState(() {
                  _linkUrl = url;
                });
              }
              Navigator.pop(context);
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _removeLink() {
    setState(() {
      _linkUrl = null;
    });
  }

  void _insertEmoji(String emoji) {
    final text = _contentController.text;
    final selection = _contentController.selection;
    final newText = text.replaceRange(selection.start, selection.end, emoji);

    if (newText.length <= _maxCharacters) {
      _contentController.text = newText;
      _contentController.selection = TextSelection.collapsed(
        offset: selection.start + emoji.length,
      );
    }
  }

  Future<List<String>> _uploadPostImages() async {
    if (_selectedImages.isEmpty) {
      return [];
    }

    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      throw Exception('User not authenticated');
    }

    List<String> imageUrls = [];

    for (int i = 0; i < _selectedImages.length; i++) {
      final image = _selectedImages[i];
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final ref = FirebaseStorage.instance
          .ref()
          .child('post_images')
          .child(user.uid)
          .child('${timestamp}_$i.jpg');

      // debugPrint(
      //   '🔄 Starting post image upload ${i + 1}/${_selectedImages.length}...',
      // );
      // debugPrint('📁 Upload path: ${ref.fullPath}');

      // Retry mechanism for image upload
      for (int attempt = 1; attempt <= 3; attempt++) {
        try {
          UploadTask uploadTask;

          if (kIsWeb) {
            final bytes = await image.readAsBytes();
            uploadTask = ref.putData(bytes);
          } else {
            uploadTask = ref.putFile(File(image.path));
          }

          // Upload with timeout
          final snapshot = await uploadTask.timeout(
            const Duration(seconds: 120),
          );

          // Verify upload was successful
          if (snapshot.state == TaskState.success) {
            final downloadUrl = await snapshot.ref.getDownloadURL().timeout(
                  const Duration(seconds: 30),
                );
            // debugPrint('✅ Post image uploaded successfully: $downloadUrl');
            // developer.log(
            //   'Post image uploaded successfully: $downloadUrl',
            //   name: 'PostImageUpload',
            // );
            imageUrls.add(downloadUrl);
            break; // Break out of retry loop for this image
          } else {
            throw Exception('Upload failed with state: ${snapshot.state}');
          }
        } on FirebaseException catch (e) {
          debugPrint(
            '❌ Firebase error on attempt $attempt: ${e.code} - ${e.message}',
          );
          // debugPrint('🔍 Full error details: $e');
          // debugPrint('📁 Attempted path: ${ref.fullPath}');
          // developer.log(
          //   'Firebase error on attempt $attempt: ${e.code} - ${e.message}',
          //   name: 'PostImageUpload',
          //   error: e,
          // );

          // Handle specific Firebase errors
          if (e.code == 'object-not-found') {
            debugPrint('🚫 Object not found error during post image upload');
          } else if (e.code == 'unauthorized') {
            debugPrint(
              '🔐 Unauthorized error - check Firebase Storage rules for post_images path',
            );
            throw Exception(
              'Unauthorized to upload image. Please check permissions.',
            );
          } else if (e.code == 'canceled') {
            debugPrint('⏹️ Post image upload was canceled');
            throw Exception('Upload was canceled.');
          }

          if (attempt == 3) {
            throw Exception(
              'Failed to upload post image after 3 attempts: ${e.message}',
            );
          }
          // Wait before retrying
          await Future.delayed(Duration(seconds: attempt * 2));
        } catch (e) {
          debugPrint('General error on attempt $attempt: $e');
          if (attempt == 3) {
            throw Exception('Failed to upload post image after 3 attempts: $e');
          }
          // Wait before retrying
          await Future.delayed(Duration(seconds: attempt * 2));
        }
      }
    }

    return imageUrls;
  }

  Future<List<String>> _uploadPostVideos() async {
    if (_selectedVideos.isEmpty) {
      return [];
    }

    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      throw Exception('User not authenticated');
    }

    List<String> videoUrls = [];

    for (int i = 0; i < _selectedVideos.length; i++) {
      final video = _selectedVideos[i];
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final ref = FirebaseStorage.instance
          .ref()
          .child('post_videos')
          .child(user.uid)
          .child('${timestamp}_$i.mp4');

      // debugPrint(
      //   '🔄 Starting post video upload ${i + 1}/${_selectedVideos.length}...',
      // );
      // debugPrint('📁 Upload path: ${ref.fullPath}');

      // Retry mechanism for video upload
      for (int attempt = 1; attempt <= 3; attempt++) {
        try {
          UploadTask uploadTask;

          if (kIsWeb) {
            final bytes = await video.readAsBytes();
            uploadTask = ref.putData(bytes);
          } else {
            uploadTask = ref.putFile(File(video.path));
          }

          // Upload with timeout (longer for videos)
          final snapshot = await uploadTask.timeout(const Duration(minutes: 5));

          // Verify upload was successful
          if (snapshot.state == TaskState.success) {
            final downloadUrl = await snapshot.ref.getDownloadURL().timeout(
                  const Duration(seconds: 30),
                );
            // debugPrint('✅ Post video uploaded successfully: $downloadUrl');
            // developer.log(
            //   'Post video uploaded successfully: $downloadUrl',
            //   name: 'PostVideoUpload',
            // );
            videoUrls.add(downloadUrl);
            break; // Break out of retry loop for this video
          } else {
            throw Exception('Upload failed with state: ${snapshot.state}');
          }
        } on FirebaseException catch (e) {
          debugPrint(
            '❌ Firebase error on attempt $attempt: ${e.code} - ${e.message}',
          );

          if (attempt == 3) {
            throw Exception(
              'Failed to upload post video after 3 attempts: ${e.message}',
            );
          }
          // Wait before retrying
          await Future.delayed(Duration(seconds: attempt * 2));
        } catch (e) {
          debugPrint('General error on attempt $attempt: $e');
          if (attempt == 3) {
            throw Exception('Failed to upload post video after 3 attempts: $e');
          }
          // Wait before retrying
          await Future.delayed(Duration(seconds: attempt * 2));
        }
      }
    }

    return videoUrls;
  }

  Future<List<String>> _uploadVideoAds() async {
    if (_selectedVideoAds.isEmpty) {
      return [];
    }

    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      throw Exception('User not authenticated');
    }

    debugPrint('🔐 User authenticated for video ad upload: ${user.uid}');
    debugPrint('📧 User email: ${user.email}');
    debugPrint('✅ User email verified: ${user.emailVerified}');

    List<String> videoAdUrls = [];

    for (int i = 0; i < _selectedVideoAds.length; i++) {
      final videoAd = _selectedVideoAds[i];
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final ref = FirebaseStorage.instance
          .ref()
          .child('video_ads')
          .child(user.uid)
          .child('${timestamp}_$i.mp4');

      debugPrint('📁 Video ad upload path: ${ref.fullPath}');
      debugPrint(
          '🔄 Starting video ad upload ${i + 1}/${_selectedVideoAds.length}...');

      // Retry mechanism for video ad upload
      for (int attempt = 1; attempt <= 3; attempt++) {
        try {
          UploadTask uploadTask;

          if (kIsWeb) {
            final bytes = await videoAd.readAsBytes();
            uploadTask = ref.putData(bytes);
          } else {
            uploadTask = ref.putFile(File(videoAd.path));
          }

          // Upload with timeout (longer for video ads)
          final snapshot = await uploadTask.timeout(const Duration(minutes: 5));

          // Verify upload was successful
          if (snapshot.state == TaskState.success) {
            final downloadUrl = await snapshot.ref.getDownloadURL().timeout(
                  const Duration(seconds: 30),
                );
            debugPrint('✅ Video ad uploaded successfully: $downloadUrl');
            videoAdUrls.add(downloadUrl);
            break; // Break out of retry loop for this video ad
          } else {
            throw Exception('Upload failed with state: ${snapshot.state}');
          }
        } on FirebaseException catch (e) {
          debugPrint(
            '❌ Firebase error on attempt $attempt: ${e.code} - ${e.message}',
          );
          debugPrint('🔍 Full Firebase error: $e');

          // Handle specific Firebase errors
          if (e.code == 'unauthorized') {
            debugPrint(
                '🔐 Unauthorized error - checking Firebase Storage rules for video_ads path');
            debugPrint('👤 Current user: ${user.uid}');
            debugPrint('📁 Attempted path: ${ref.fullPath}');
          }

          if (attempt == 3) {
            throw Exception(
              'Failed to upload video ad after 3 attempts: ${e.message}',
            );
          }
          // Wait before retrying
          await Future.delayed(Duration(seconds: attempt * 2));
        } catch (e) {
          debugPrint('General error on attempt $attempt: $e');
          if (attempt == 3) {
            throw Exception('Failed to upload video ad after 3 attempts: $e');
          }
          // Wait before retrying
          await Future.delayed(Duration(seconds: attempt * 2));
        }
      }
    }

    return videoAdUrls;
  }

  Map<String, dynamic> _getCategoryData(String categoryName) {
    return _categories.firstWhere(
      (cat) => cat['name'] == categoryName,
      orElse: () => _categories[0],
    );
  }

  Future<void> _publishPost() async {
    if (_isSubmitting) return;

    // Check user status before allowing post creation
    if (!await UserStatusGuard.checkAccess()) {
      return; // User is blocked, guard will handle redirect
    }

    if (_contentController.text.trim().isEmpty) {
      _showErrorMessage('Please write something to share');
      return;
    }

    if (_contentController.text.length > _maxCharacters) {
      _showErrorMessage('Post exceeds $_maxCharacters character limit');
      return;
    }

    // Category is always available from CategoryController
    final selectedCategory = _categoryController.selectedCategoryName;

    // Check wallet balance
    final currentBalance = walletController.balance;
    if (_postPrice > currentBalance) {
      _showErrorMessage(
        'Insufficient funds. Your balance: ${walletController.formatCurrency(currentBalance)}',
        showAddFunds: true,
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
      _isLoading = true;
    });

    try {
      // Upload images if selected
      List<String> imageUrls = [];
      if (_selectedImages.isNotEmpty) {
        try {
          imageUrls = await _uploadPostImages();
          // debugPrint('Images uploaded successfully for post: $imageUrls');
        } catch (e) {
          debugPrint('Image upload failed: $e');
          _showErrorMessage('Failed to upload images: $e');
          return; // Don't proceed if image upload fails
        }
      }

      // Upload videos if selected
      List<String> videoUrls = [];
      if (_selectedVideos.isNotEmpty) {
        try {
          videoUrls = await _uploadPostVideos();
          // debugPrint('Videos uploaded successfully for post: $videoUrls');
        } catch (e) {
          debugPrint('Video upload failed: $e');
          _showErrorMessage('Failed to upload videos: $e');
          return; // Don't proceed if video upload fails
        }
      }

      // Upload video ads if selected
      List<String> videoAdUrls = [];
      if (_selectedVideoAds.isNotEmpty) {
        try {
          videoAdUrls = await _uploadVideoAds();
          // debugPrint('Video ads uploaded successfully for post: $videoAdUrls');
        } catch (e) {
          debugPrint('Video ad upload failed: $e');
          _showErrorMessage('Failed to upload video ads: $e');
          return; // Don't proceed if video ad upload fails
        }
      }

      // Combine regular videos and video ads
      final allVideoUrls = [...videoUrls, ...videoAdUrls];

      // Create the post first with images, videos and link
      final postId = await _postController.createPost(
        content: _contentController.text.trim(),
        price: _postPrice,
        category: selectedCategory,
        tags: [],
        isPublic: true,
        allowComments: true,
        imageUrls: imageUrls,
        videoUrls: allVideoUrls,
        linkUrl: _linkUrl,
        hasPoll: _hasPoll,
      );

      // Deduct balance for post creation AFTER post is created successfully
      final balanceDeducted = await walletController.deductFunds(
        amount: _postPrice,
        description: 'Post creation in $selectedCategory',
        postId: postId, // Use the real postId
      );

      if (!balanceDeducted) {
        // If payment fails, we should ideally delete the post, but for now just throw
        throw Exception('Failed to process payment. Please try again.');
      }

      if (mounted) {
        HapticFeedback.mediumImpact();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Post published in $selectedCategory! Amount: ${walletController.formatCurrency(_postPrice)}',
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );

        // Clear form fields after successful post
        _contentController.clear();
        _selectedImages.clear();
        _selectedVideos.clear();
        _selectedVideoAds.clear();
        _linkUrl = null;

        // Clear auto-draft since post was published
        _draftController.clearAutoDraft();

        // Always navigate to /home (Main tab) after successful post
        Navigator.of(context)
            .pushNamedAndRemoveUntil('/home', (route) => false);
      }
    } catch (e) {
      if (mounted) {
        _showErrorMessage(
          'Failed to create post: ${e.toString().replaceAll('Exception: ', '')}',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
          _isLoading = false;
        });
      }
    }
  }

  void _showErrorMessage(String message, {bool showAddFunds = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
        action: showAddFunds
            ? SnackBarAction(
                label: 'ReUp!',
                textColor: Colors.white,
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const WalletScreen(),
                    ),
                  ).then((_) => setState(() {}));
                },
              )
            : null,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final categoryData = _getCategoryData(
        _categoryController.selectedCategoryName,
      );
      final categoryColor = categoryData['color'] as Color;

      return Scaffold(
        backgroundColor: Colors.grey[50],
        body: SafeArea(
          child: LayoutBuilder(
            builder: (context, constraints) {
              return Center(
                child: Container(
                  constraints: BoxConstraints(
                    maxWidth: kIsWeb ? 800 : double.infinity,
                  ),
                  margin: kIsWeb
                      ? const EdgeInsets.symmetric(horizontal: 24)
                      : EdgeInsets.zero,
                  child: Column(
                    children: [
                      // Header Section
                      Container(
                        color: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 16,
                        ),
                        child: Column(
                          children: [
                            // Top Header with Logo and Draft Button
                            Row(
                              children: [
                                // // Draft button
                                // IconButton(
                                //   onPressed: _showDraftDialog,
                                //   icon: const Icon(Icons.drafts_outlined),
                                //   tooltip: 'Drafts',
                                // ),

                                // Centered Logo
                                Expanded(
                                  child: Center(
                                    child: Container(
                                      width: 50,
                                      height: 50,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(25),
                                      ),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(25),
                                        child: Image.asset(
                                          'assets/images/money_mouth.png',
                                          width: 50,
                                          height: 50,
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),

                                // // Save draft button
                                // IconButton(
                                //   onPressed: _saveDraft,
                                //   icon: const Icon(Icons.save_outlined),
                                //   tooltip: 'Save Draft',
                                // ),
                              ],
                            ),

                            const SizedBox(height: 16),
                          ],
                        ),
                      ),

                      // Main Content Area
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(0),
                          child: _buildExpandedView(categoryColor),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      );
    });
  }

  Widget _buildExpandedView(Color categoryColor) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 2),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          // mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Center(
              child: Text(
                _categoryController.selectedCategoryName,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ),

            const SizedBox(height: 12),

            // Content Input
            Container(
              padding: const EdgeInsets.all(12),
              constraints: BoxConstraints(
                minHeight: double.minPositive,
              ),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Column(
                children: [
                  // Text input area

                  SizedBox(
                    height: 200, // Fixed height for text input area
                    child: TextFormField(
                      controller: _contentController,
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.zero,
                      ),
                      style: TextStyle(fontSize: 16, color: Colors.grey[700]),
                      maxLines: null,
                      textAlignVertical: TextAlignVertical.top,
                      onChanged: (value) {
                        setState(() {
                          // This will trigger a rebuild and update character count in real-time
                        });

                        // Auto-save draft
                        _draftController.startAutoSave(
                          content: value,
                          price: _postPrice,
                          category: _categoryController.selectedCategoryName,
                          isPublic: true,
                          allowComments: true,
                          linkUrl: _linkUrl,
                          images: _selectedImages,
                          videos: _selectedVideos,
                          hasPoll: _hasPoll,
                        );
                      },
                    ),
                  ),
                  // Show selected attachments preview inside container
                  if (_selectedImages.isNotEmpty ||
                      _selectedVideos.isNotEmpty ||
                      _selectedVideoAds.isNotEmpty ||
                      _linkUrl != null ||
                      _hasPoll) ...[
                    const SizedBox(height: 12),
                    _buildMediaPreview(),
                  ],

                  // // Show poll configuration when poll is enabled
                  // if (_hasPoll) ...[
                  //   const SizedBox(height: 12),
                  //   _buildPollConfiguration(),
                  // ],
                  // const SizedBox(height: 20),
                  // Media upload buttons inside the container at bottom
                  const SizedBox(height: 12),
                  _buildMediaButtonsRow(),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Character count
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${_contentController.text.length}/$_maxCharacters characters',
                  style: TextStyle(
                    fontSize: 12,
                    color: _contentController.text.length > _maxCharacters
                        ? Colors.red
                        : Colors.grey[500],
                  ),
                ),
                Text(
                  'Cost: ${walletController.formatCurrency(_postPrice)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: categoryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),

            // Put Up Button
            SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                onPressed: (_isLoading || _isSubmitting) ? null : _publishPost,
                style: ElevatedButton.styleFrom(
                  backgroundColor: categoryColor,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: (_isLoading || _isSubmitting)
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : const Text(
                        'Put Up',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaPreview() {
    return SizedBox(
      height: 50,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            // Show selected images with preview
            ..._selectedImages.asMap().entries.map((entry) {
              final index = entry.key;
              final image = entry.value;
              return Container(
                width: 40,
                height: 40,
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: kIsWeb
                          ? FutureBuilder<Uint8List>(
                              future: image.readAsBytes(),
                              builder: (context, snapshot) {
                                if (snapshot.hasData) {
                                  return Image.memory(
                                    snapshot.data!,
                                    width: 40,
                                    height: 40,
                                    fit: BoxFit.cover,
                                  );
                                }
                                return Container(
                                  width: 40,
                                  height: 40,
                                  color: Colors.grey[200],
                                  child: const Icon(Icons.image, size: 20),
                                );
                              },
                            )
                          : Image.file(
                              File(image.path),
                              width: 40,
                              height: 40,
                              fit: BoxFit.cover,
                            ),
                    ),
                    // Remove button
                    Positioned(
                      top: 2,
                      right: 2,
                      child: GestureDetector(
                        onTap: () => _removeImage(index),
                        child: Container(
                          width: 16,
                          height: 16,
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            size: 10,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }),

            // Show selected videos with preview
            ..._selectedVideos.asMap().entries.map((entry) {
              final index = entry.key;
              return Container(
                width: 40,
                height: 40,
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                  color: Colors.grey[200],
                ),
                child: Stack(
                  children: [
                    const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.videocam, size: 16, color: Colors.blue),
                          SizedBox(height: 2),
                          Text(
                            'Video',
                            style: TextStyle(fontSize: 8, color: Colors.blue),
                          ),
                        ],
                      ),
                    ),
                    Positioned(
                      top: 2,
                      right: 2,
                      child: GestureDetector(
                        onTap: () => _removeVideo(index),
                        child: Container(
                          width: 16,
                          height: 16,
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            size: 10,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }),

            // Show selected video ads with preview
            ..._selectedVideoAds.asMap().entries.map((entry) {
              final index = entry.key;
              return Container(
                width: 40,
                height: 40,
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange[300]!),
                  color: Colors.orange[50],
                ),
                child: Stack(
                  children: [
                    const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.ads_click, size: 16, color: Colors.orange),
                          SizedBox(height: 2),
                          Text(
                            'Ad',
                            style: TextStyle(
                              fontSize: 8,
                              color: Colors.orange,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Positioned(
                      top: 2,
                      right: 2,
                      child: GestureDetector(
                        onTap: () => _removeVideoAd(index),
                        child: Container(
                          width: 16,
                          height: 16,
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            size: 10,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }),

            // Show link if added
            if (_linkUrl != null)
              Container(
                width: 40,
                height: 40,
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue[300]!),
                  color: Colors.blue[50],
                ),
                child: Stack(
                  children: [
                    const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.link, size: 16, color: Colors.blue),
                          SizedBox(height: 2),
                          Text(
                            'Link',
                            style: TextStyle(fontSize: 8, color: Colors.blue),
                          ),
                        ],
                      ),
                    ),
                    Positioned(
                      top: 2,
                      right: 2,
                      child: GestureDetector(
                        onTap: _removeLink,
                        child: Container(
                          width: 16,
                          height: 16,
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            size: 10,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            // Show poll if enabled
            if (_hasPoll)
              Container(
                width: 40,
                height: 40,
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[300]!),
                  color: Colors.green[50],
                ),
                child: Stack(
                  children: [
                    const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.poll, size: 16, color: Colors.green),
                          SizedBox(height: 2),
                          Text(
                            'Poll',
                            style: TextStyle(fontSize: 8, color: Colors.green),
                          ),
                        ],
                      ),
                    ),
                    Positioned(
                      top: 2,
                      right: 2,
                      child: GestureDetector(
                        onTap: () => setState(() => _hasPoll = false),
                        child: Container(
                          width: 16,
                          height: 16,
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            size: 10,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaButtonsRow() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      alignment: WrapAlignment.start,
      direction: Axis.horizontal,
      runAlignment: WrapAlignment.start,
      children: [
        _buildMediaButton(
          icon: Icons.photo_library,
          label: 'Photo',
          onTap: _pickImage,
          color: Colors.purple,
          textColor: Colors.white,
        ),
        _buildMediaButton(
          icon: Icons.camera_alt,
          label: 'Camera',
          onTap: _takePhoto,
          color: Colors.teal,
          textColor: Colors.white,
        ),
        _buildMediaButton(
          icon: Icons.videocam,
          label:
              _selectedVideos.isNotEmpty ? 'Video(1/1)' : 'Video(30)', // 60 Sec
          onTap: _selectedVideos.isNotEmpty ? null : _showVideoOptions,
          color: Colors.indigo,
          textColor: Colors.white,
        ),
        _buildMediaButton(
          icon: Icons.ads_click,
          label: _selectedVideoAds.isNotEmpty
              ? 'Video Ad (1/1)'
              : 'Video Ad(60)', // 60 sec
          onTap: _selectedVideoAds.isNotEmpty ? null : _showVideoAdOptions,
          color: Colors.orange,
          textColor: Colors.white,
        ),
        _buildMediaButton(
          icon: Icons.link,
          label: 'Link',
          onTap: _showAddLinkDialog,
          color: Colors.blue,
          textColor: Colors.white,
        ),
        _buildMediaButton(
          icon: Icons.emoji_emotions,
          label: 'Emoji',
          onTap: _showEmojiPicker,
          color: Colors.pink,
          textColor: Colors.white,
        ),
        _buildMediaButton(
          icon: Icons.poll,
          label: _hasPoll ? 'Poll ✓' : 'Poll',
          onTap: _togglePoll,
          color: Colors.green,
          textColor: Colors.white,
        ),
      ],
    );
  }

  Widget _buildMediaButton({
    required IconData icon,
    required String label,
    required VoidCallback? onTap,
    Color? color,
    Color? textColor,
  }) {
    final isDisabled = onTap == null;
    final buttonColor = color ?? Colors.grey[400]!;
    final finalTextColor = textColor ?? Colors.grey[700]!;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        decoration: BoxDecoration(
          color: isDisabled ? Colors.grey[200] : buttonColor,
          borderRadius: BorderRadius.circular(20),
          boxShadow: isDisabled
              ? null
              : [
                  BoxShadow(
                    color: buttonColor.withOpacity(0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 14,
              color: isDisabled ? Colors.grey[400] : finalTextColor,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: isDisabled ? Colors.grey[400] : finalTextColor,
                // fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showEmojiPicker() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        height: 250,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Select Emoji',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: GridView.count(
                crossAxisCount: 8,
                children: [
                  '😀',
                  '😃',
                  '😄',
                  '😁',
                  '😆',
                  '😅',
                  '😂',
                  '🤣',
                  '😊',
                  '😇',
                  '🙂',
                  '🙃',
                  '😉',
                  '😌',
                  '😍',
                  '🥰',
                  '😘',
                  '😗',
                  '😙',
                  '😚',
                  '😋',
                  '😛',
                  '😝',
                  '😜',
                  '🤪',
                  '🤨',
                  '🧐',
                  '🤓',
                  '😎',
                  '🤩',
                  '🥳',
                  '😏',
                  '😒',
                  '😞',
                  '😔',
                  '😟',
                  '😕',
                  '🙁',
                  '☹️',
                  '😣',
                  '😖',
                  '😫',
                  '😩',
                  '🥺',
                  '😢',
                  '😭',
                  '😤',
                  '😠',
                  '😡',
                  '🤬',
                  '🤯',
                  '😳',
                  '🥵',
                  '🥶',
                  '😱',
                  '😨',
                  '😰',
                  '😥',
                  '😓',
                  '🤗',
                  '🤔',
                  '🤭',
                  '🤫',
                  '🤥'
                ]
                    .map(
                      (emoji) => GestureDetector(
                        onTap: () {
                          _insertEmoji(emoji);
                          Navigator.pop(context);
                        },
                        child: Container(
                          margin: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            color: Colors.grey[100],
                          ),
                          child: Center(
                            child: Text(
                              emoji,
                              style: const TextStyle(fontSize: 20),
                            ),
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _togglePoll() {
    setState(() {
      _hasPoll = !_hasPoll;
    });
  }

  /// Show video options dialog (record or upload)
  void _showVideoOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Add Video (30s)',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.videocam, color: Colors.blue),
              title: const Text('Upload from Gallery'),
              subtitle: const Text('Choose existing video'),
              onTap: () {
                Navigator.pop(context);
                _pickVideo();
              },
            ),
            ListTile(
              leading: const Icon(Icons.video_camera_back, color: Colors.red),
              title: const Text('Record Video'),
              subtitle: const Text('Record new video'),
              onTap: () {
                Navigator.pop(context);
                _recordVideo();
              },
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  /// Show video ad options dialog (record or upload)
  void _showVideoAdOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Add Video Ad (60s)',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Price will be set to Top Paid Post + \$1',
              style: TextStyle(
                fontSize: 14,
                color: Colors.orange,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.ads_click, color: Colors.orange),
              title: const Text('Upload from Gallery'),
              subtitle: const Text('Choose existing video ad'),
              onTap: () {
                Navigator.pop(context);
                _pickVideoAd();
              },
            ),
            ListTile(
              leading: const Icon(Icons.video_camera_back, color: Colors.red),
              title: const Text('Record Video Ad'),
              subtitle: const Text('Record new video ad'),
              onTap: () {
                Navigator.pop(context);
                _recordVideoAd();
              },
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }
}
