import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/admin_auth_controller.dart';
import '../routes/admin_routes.dart';

class AdminAuthMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    // Check if AdminAuthController is initialized
    if (!Get.isRegistered<AdminAuthController>()) {
      Get.put(AdminAuthController());
    }

    final adminAuthController = Get.find<AdminAuthController>();

    // If not authenticated, redirect to admin login
    if (!adminAuthController.isAuthenticated) {
      return const RouteSettings(name: AdminRoutes.login);
    }

    // Check specific permissions for certain routes
    if (route != null) {
      if (route == AdminRoutes.users && !adminAuthController.canManageUsers) {
        Get.snackbar(
          'Access Denied',
          'You don\'t have permission to manage users',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return const RouteSettings(name: AdminRoutes.dashboard);
      }

      if (route == AdminRoutes.wallets &&
          !adminAuthController.canManageWallets) {
        Get.snackbar(
          'Access Denied',
          'You don\'t have permission to manage wallets',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return const RouteSettings(name: AdminRoutes.dashboard);
      }

      if (route == AdminRoutes.admins && !adminAuthController.canManageAdmins) {
        Get.snackbar(
          'Access Denied',
          'You don\'t have permission to manage admins',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return const RouteSettings(name: AdminRoutes.dashboard);
      }
    }

    return null; // Allow access
  }
}
