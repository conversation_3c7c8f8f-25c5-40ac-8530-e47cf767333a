import 'package:cloud_firestore/cloud_firestore.dart';

enum UserStatus {
  active,
  blocked,
}

enum UserActivityType {
  login,
  logout,
  postCreated,
  postDeleted,
  walletTransaction,
  profileUpdate,
  passwordChange,
}

class UserManagementModel {
  final String id;
  final String email;
  final String? name;
  final String? username;
  final String? profileImageUrl;
  final String? bio;
  final UserStatus status;
  final DateTime createdAt;
  final DateTime? lastLogin;
  final bool emailVerified;
  final bool profileCompleted;
  final Map<String, dynamic>? metadata;

  const UserManagementModel({
    required this.id,
    required this.email,
    this.name,
    this.username,
    this.profileImageUrl,
    this.bio,
    required this.status,
    required this.createdAt,
    this.lastLogin,
    required this.emailVerified,
    required this.profileCompleted,
    this.metadata,
  });

  Map<String, dynamic> toMap() {
    return {
      'email': email,
      'name': name,
      'username': username,
      'profileImageUrl': profileImageUrl,
      'bio': bio,
      'status': status.name,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastLogin': lastLogin?.millisecondsSinceEpoch,
      'emailVerified': emailVerified,
      'profileCompleted': profileCompleted,
      'metadata': metadata,
    };
  }

  factory UserManagementModel.fromMap(String id, Map<String, dynamic> map) {
    // Handle timestamp conversion
    DateTime createdAt;
    final createdAtValue = map['createdAt'];
    if (createdAtValue is int) {
      createdAt = DateTime.fromMillisecondsSinceEpoch(createdAtValue);
    } else if (createdAtValue is Timestamp) {
      createdAt = createdAtValue.toDate();
    } else {
      createdAt = DateTime.now();
    }

    DateTime? lastLogin;
    final lastLoginValue = map['lastLogin'];
    if (lastLoginValue is int) {
      lastLogin = DateTime.fromMillisecondsSinceEpoch(lastLoginValue);
    } else if (lastLoginValue is Timestamp) {
      lastLogin = lastLoginValue.toDate();
    }

    return UserManagementModel(
      id: id,
      email: map['email'] ?? '',
      name: map['name'],
      username: map['username'],
      profileImageUrl: map['profileImageUrl'] ?? map['photoUrl'],
      bio: map['bio'],
      status: UserStatus.values.firstWhere(
        (status) => status.name == map['status'],
        orElse: () => UserStatus.active,
      ),
      createdAt: createdAt,
      lastLogin: lastLogin,
      emailVerified: map['emailVerified'] ?? false,
      profileCompleted: map['profileCompleted'] ?? false,
      metadata: map['metadata'],
    );
  }

  UserManagementModel copyWith({
    String? email,
    String? name,
    String? username,
    String? profileImageUrl,
    String? bio,
    UserStatus? status,
    DateTime? lastLogin,
    bool? emailVerified,
    bool? profileCompleted,
    Map<String, dynamic>? metadata,
  }) {
    return UserManagementModel(
      id: id,
      email: email ?? this.email,
      name: name ?? this.name,
      username: username ?? this.username,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      bio: bio ?? this.bio,
      status: status ?? this.status,
      createdAt: createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
      emailVerified: emailVerified ?? this.emailVerified,
      profileCompleted: profileCompleted ?? this.profileCompleted,
      metadata: metadata ?? this.metadata,
    );
  }

  bool get isBlocked => status == UserStatus.blocked;
  bool get isActive => status == UserStatus.active;
}

class UserActivityModel {
  final String id;
  final String userId;
  final UserActivityType type;
  final String description;
  final DateTime timestamp;
  final String? ipAddress;
  final String? userAgent;
  final Map<String, dynamic>? metadata;

  const UserActivityModel({
    required this.id,
    required this.userId,
    required this.type,
    required this.description,
    required this.timestamp,
    this.ipAddress,
    this.userAgent,
    this.metadata,
  });

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'type': type.name,
      'description': description,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'ipAddress': ipAddress,
      'userAgent': userAgent,
      'metadata': metadata,
    };
  }

  factory UserActivityModel.fromMap(String id, Map<String, dynamic> map) {
    DateTime timestamp;
    final timestampValue = map['timestamp'];
    if (timestampValue is int) {
      timestamp = DateTime.fromMillisecondsSinceEpoch(timestampValue);
    } else if (timestampValue is Timestamp) {
      timestamp = timestampValue.toDate();
    } else {
      timestamp = DateTime.now();
    }

    return UserActivityModel(
      id: id,
      userId: map['userId'] ?? '',
      type: UserActivityType.values.firstWhere(
        (type) => type.name == map['type'],
        orElse: () => UserActivityType.login,
      ),
      description: map['description'] ?? '',
      timestamp: timestamp,
      ipAddress: map['ipAddress'],
      userAgent: map['userAgent'],
      metadata: map['metadata'],
    );
  }
}
