import 'package:cloud_firestore/cloud_firestore.dart';

enum AdminRole {
  superAdmin,
  admin,
  moderator,
}

enum AdminStatus {
  active,
  inactive,
  suspended,
}

class AdminUserModel {
  final String id;
  final String email;
  final String name;
  final AdminRole role;
  final AdminStatus status;
  final DateTime createdAt;
  final DateTime? lastLogin;
  final List<String> permissions;
  final Map<String, dynamic>? metadata;

  const AdminUserModel({
    required this.id,
    required this.email,
    required this.name,
    required this.role,
    required this.status,
    required this.createdAt,
    this.lastLogin,
    this.permissions = const [],
    this.metadata,
  });

  Map<String, dynamic> toMap() {
    return {
      'email': email,
      'name': name,
      'role': role.name,
      'status': status.name,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastLogin': lastLogin?.millisecondsSinceEpoch,
      'permissions': permissions,
      'metadata': metadata,
    };
  }

  factory AdminUserModel.fromMap(String id, Map<String, dynamic> map) {
    // Handle timestamp conversion
    DateTime createdAt;
    final createdAtValue = map['createdAt'];
    if (createdAtValue is int) {
      createdAt = DateTime.fromMillisecondsSinceEpoch(createdAtValue);
    } else if (createdAtValue is Timestamp) {
      createdAt = createdAtValue.toDate();
    } else {
      createdAt = DateTime.now();
    }

    DateTime? lastLogin;
    final lastLoginValue = map['lastLogin'];
    if (lastLoginValue is int) {
      lastLogin = DateTime.fromMillisecondsSinceEpoch(lastLoginValue);
    } else if (lastLoginValue is Timestamp) {
      lastLogin = lastLoginValue.toDate();
    }

    return AdminUserModel(
      id: id,
      email: map['email'] ?? '',
      name: map['name'] ?? '',
      role: AdminRole.values.firstWhere(
        (role) => role.name == map['role'],
        orElse: () => AdminRole.moderator,
      ),
      status: AdminStatus.values.firstWhere(
        (status) => status.name == map['status'],
        orElse: () => AdminStatus.active,
      ),
      createdAt: createdAt,
      lastLogin: lastLogin,
      permissions: List<String>.from(map['permissions'] ?? []),
      metadata: map['metadata'],
    );
  }

  AdminUserModel copyWith({
    String? email,
    String? name,
    AdminRole? role,
    AdminStatus? status,
    DateTime? lastLogin,
    List<String>? permissions,
    Map<String, dynamic>? metadata,
  }) {
    return AdminUserModel(
      id: id,
      email: email ?? this.email,
      name: name ?? this.name,
      role: role ?? this.role,
      status: status ?? this.status,
      createdAt: createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
      permissions: permissions ?? this.permissions,
      metadata: metadata ?? this.metadata,
    );
  }

  bool hasPermission(String permission) {
    return permissions.contains(permission) || role == AdminRole.superAdmin;
  }

  bool get canManageUsers => hasPermission('manage_users');
  bool get canManageWallets => hasPermission('manage_wallets');
  bool get canViewAnalytics => hasPermission('view_analytics');
  bool get canManageAdmins => role == AdminRole.superAdmin;
}
