import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_management_model.dart';

class UserActivityController extends GetxController {
  static UserActivityController get to => Get.find();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Reactive variables
  final _activities = <UserActivityModel>[].obs;
  final _filteredActivities = <UserActivityModel>[].obs;
  final _userDetails = <String, Map<String, dynamic>>{}.obs;
  final _isLoading = false.obs;
  final _errorMessage = RxnString();
  final _searchQuery = ''.obs;
  final _selectedActivityType = Rxn<UserActivityType>();
  final _selectedDateRange = Rxn<DateTimeRange>();
  final _totalActivities = 0.obs;
  final _todayActivities = 0.obs;
  final _onlineUsers = 0.obs;

  StreamSubscription<QuerySnapshot>? _activitiesSubscription;
  StreamSubscription<QuerySnapshot>? _usersSubscription;

  // Getters
  List<UserActivityModel> get activities => _filteredActivities;
  Map<String, Map<String, dynamic>> get userDetails => _userDetails;
  bool get isLoading => _isLoading.value;
  String? get errorMessage => _errorMessage.value;
  String get searchQuery => _searchQuery.value;
  UserActivityType? get selectedActivityType => _selectedActivityType.value;
  DateTimeRange? get selectedDateRange => _selectedDateRange.value;
  int get totalActivities => _totalActivities.value;
  int get todayActivities => _todayActivities.value;
  int get onlineUsers => _onlineUsers.value;

  @override
  void onInit() {
    super.onInit();
    _initializeActivitiesStream();
    _initializeOnlineUsersStream();
    _setupSearchListener();
  }

  @override
  void onClose() {
    _activitiesSubscription?.cancel();
    _usersSubscription?.cancel();
    super.onClose();
  }

  void _initializeActivitiesStream() {
    _isLoading.value = true;

    _activitiesSubscription = _firestore
        .collection('user_activities')
        .orderBy('timestamp', descending: true)
        .limit(500) // Limit to recent activities
        .snapshots()
        .listen(
      (snapshot) {
        _processActivitiesSnapshot(snapshot);
        _isLoading.value = false;
      },
      onError: (error) {
        debugPrint('Error listening to activities: $error');
        _errorMessage.value = 'Failed to load activities';
        _isLoading.value = false;
      },
    );
  }

  void _initializeOnlineUsersStream() {
    // Listen to users with recent activity (last 5 minutes)
    final fiveMinutesAgo = DateTime.now().subtract(const Duration(minutes: 5));

    _usersSubscription = _firestore
        .collection('users')
        .where('lastActivity',
            isGreaterThan: Timestamp.fromDate(fiveMinutesAgo))
        .snapshots()
        .listen(
      (snapshot) {
        _onlineUsers.value = snapshot.docs.length;
      },
      onError: (error) {
        debugPrint('Error listening to online users: $error');
      },
    );
  }

  void _processActivitiesSnapshot(QuerySnapshot snapshot) {
    final activitiesList = snapshot.docs.map((doc) {
      return UserActivityModel.fromMap(
          doc.id, doc.data() as Map<String, dynamic>);
    }).toList();

    _activities.value = activitiesList;
    _loadUserDetails();
    _updateStatistics();
    _applyFilters();
  }

  void _updateStatistics() {
    _totalActivities.value = _activities.length;

    // Count today's activities
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);

    _todayActivities.value = _activities.where((activity) {
      return activity.timestamp.isAfter(startOfDay);
    }).length;
  }

  void _loadUserDetails() async {
    final uniqueUserIds =
        _activities.map((activity) => activity.userId).toSet();

    for (final userId in uniqueUserIds) {
      if (!_userDetails.containsKey(userId)) {
        try {
          final userDoc =
              await _firestore.collection('users').doc(userId).get();
          if (userDoc.exists) {
            _userDetails[userId] = userDoc.data()!;
          }
        } catch (e) {
          debugPrint('Error loading user details for $userId: $e');
        }
      }
    }
  }

  void _setupSearchListener() {
    debounce(_searchQuery, (_) => _applyFilters(),
        time: const Duration(milliseconds: 300));
    ever(_selectedActivityType, (_) => _applyFilters());
    ever(_selectedDateRange, (_) => _applyFilters());
  }

  void _applyFilters() {
    var filtered = _activities.toList();

    // Apply search filter
    if (_searchQuery.value.isNotEmpty) {
      final query = _searchQuery.value.toLowerCase();
      filtered = filtered.where((activity) {
        final userDetail = _userDetails[activity.userId];
        return activity.userId.toLowerCase().contains(query) ||
            activity.description.toLowerCase().contains(query) ||
            (userDetail?['name']?.toString().toLowerCase().contains(query) ??
                false) ||
            (userDetail?['username']
                    ?.toString()
                    .toLowerCase()
                    .contains(query) ??
                false) ||
            (userDetail?['email']?.toString().toLowerCase().contains(query) ??
                false);
      }).toList();
    }

    // Apply activity type filter
    if (_selectedActivityType.value != null) {
      filtered = filtered
          .where((activity) => activity.type == _selectedActivityType.value)
          .toList();
    }

    // Apply date range filter
    if (_selectedDateRange.value != null) {
      final range = _selectedDateRange.value!;
      filtered = filtered.where((activity) {
        return activity.timestamp.isAfter(range.start) &&
            activity.timestamp.isBefore(range.end.add(const Duration(days: 1)));
      }).toList();
    }

    _filteredActivities.value = filtered;
  }

  void setSearchQuery(String query) {
    _searchQuery.value = query;
  }

  void setActivityTypeFilter(UserActivityType? type) {
    _selectedActivityType.value = type;
  }

  void setDateRangeFilter(DateTimeRange? range) {
    _selectedDateRange.value = range;
  }

  void clearFilters() {
    _searchQuery.value = '';
    _selectedActivityType.value = null;
    _selectedDateRange.value = null;
  }

  Future<void> logUserActivity({
    required String userId,
    required UserActivityType type,
    required String description,
    String? ipAddress,
    String? userAgent,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      await _firestore.collection('user_activities').add({
        'userId': userId,
        'type': type.name,
        'description': description,
        'timestamp': FieldValue.serverTimestamp(),
        'ipAddress': ipAddress,
        'userAgent': userAgent,
        'metadata': metadata,
      });
    } catch (e) {
      debugPrint('Error logging user activity: $e');
    }
  }

  Future<List<UserActivityModel>> getUserActivities(String userId,
      {int limit = 50}) async {
    try {
      final snapshot = await _firestore
          .collection('user_activities')
          .where('userId', isEqualTo: userId)
          .orderBy('timestamp', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs.map((doc) {
        return UserActivityModel.fromMap(doc.id, doc.data());
      }).toList();
    } catch (e) {
      debugPrint('Error getting user activities: $e');
      return [];
    }
  }

  Future<Map<String, int>> getActivityStatsByType(
      {DateTime? startDate, DateTime? endDate}) async {
    try {
      var query = _firestore.collection('user_activities').orderBy('timestamp');

      if (startDate != null) {
        query = query.where('timestamp',
            isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }

      if (endDate != null) {
        query = query.where('timestamp',
            isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      final snapshot = await query.get();
      final stats = <String, int>{};

      for (final doc in snapshot.docs) {
        final data = doc.data();
        final type = data['type'] as String;
        stats[type] = (stats[type] ?? 0) + 1;
      }

      return stats;
    } catch (e) {
      debugPrint('Error getting activity stats: $e');
      return {};
    }
  }

  Future<Map<String, int>> getHourlyActivityStats({DateTime? date}) async {
    try {
      final targetDate = date ?? DateTime.now();
      final startOfDay =
          DateTime(targetDate.year, targetDate.month, targetDate.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final snapshot = await _firestore
          .collection('user_activities')
          .where('timestamp',
              isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .where('timestamp', isLessThan: Timestamp.fromDate(endOfDay))
          .get();

      final hourlyStats = <String, int>{};

      // Initialize all hours
      for (int i = 0; i < 24; i++) {
        hourlyStats[i.toString().padLeft(2, '0')] = 0;
      }

      for (final doc in snapshot.docs) {
        final data = doc.data();
        final timestamp = (data['timestamp'] as Timestamp).toDate();
        final hour = timestamp.hour.toString().padLeft(2, '0');
        hourlyStats[hour] = (hourlyStats[hour] ?? 0) + 1;
      }

      return hourlyStats;
    } catch (e) {
      debugPrint('Error getting hourly activity stats: $e');
      return {};
    }
  }

  Future<List<Map<String, dynamic>>> getTopActiveUsers({int limit = 10}) async {
    try {
      final snapshot = await _firestore
          .collection('user_activities')
          .where('timestamp',
              isGreaterThan: Timestamp.fromDate(
                  DateTime.now().subtract(const Duration(days: 7))))
          .get();

      final userActivityCount = <String, int>{};

      for (final doc in snapshot.docs) {
        final data = doc.data();
        final userId = data['userId'] as String;
        userActivityCount[userId] = (userActivityCount[userId] ?? 0) + 1;
      }

      // Sort by activity count and take top users
      final sortedUsers = userActivityCount.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      final topUsers = <Map<String, dynamic>>[];

      for (int i = 0; i < limit && i < sortedUsers.length; i++) {
        final entry = sortedUsers[i];

        // Get user details
        try {
          final userDoc =
              await _firestore.collection('users').doc(entry.key).get();

          if (userDoc.exists) {
            final userData = userDoc.data()!;
            topUsers.add({
              'userId': entry.key,
              'activityCount': entry.value,
              'name': userData['name'] ?? 'Unknown',
              'email': userData['email'] ?? '',
              'username': userData['username'] ?? '',
            });
          }
        } catch (e) {
          debugPrint('Error getting user details for ${entry.key}: $e');
        }
      }

      return topUsers;
    } catch (e) {
      debugPrint('Error getting top active users: $e');
      return [];
    }
  }

  String getUserDisplayName(String userId) {
    final userDetail = _userDetails[userId];
    if (userDetail != null) {
      final name = userDetail['name']?.toString();
      final username = userDetail['username']?.toString();
      final email = userDetail['email']?.toString();

      if (name != null && name.isNotEmpty) {
        return name;
      } else if (username != null && username.isNotEmpty) {
        return '@$username';
      } else if (email != null && email.isNotEmpty) {
        return email;
      }
    }
    return userId;
  }

  void clearError() {
    _errorMessage.value = null;
  }
}
