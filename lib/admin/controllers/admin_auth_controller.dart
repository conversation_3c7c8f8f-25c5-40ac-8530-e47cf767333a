import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/admin_user_model.dart';

class AdminAuthController extends GetxController {
  static AdminAuthController get to => Get.find();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Reactive variables
  final _currentAdmin = Rxn<AdminUserModel>();
  final _isLoading = false.obs;
  final _isAuthenticated = false.obs;
  final _errorMessage = RxnString();

  // Getters
  AdminUserModel? get currentAdmin => _currentAdmin.value;
  bool get isLoading => _isLoading.value;
  bool get isAuthenticated => _isAuthenticated.value;
  String? get errorMessage => _errorMessage.value;

  @override
  void onInit() {
    super.onInit();
    _checkAuthState();
  }

  void _checkAuthState() {
    _auth.authStateChanges().listen((User? user) async {
      if (user != null) {
        await _loadAdminData(user.uid);
      } else {
        _currentAdmin.value = null;
        _isAuthenticated.value = false;
      }
    });
  }

  Future<void> _loadAdminData(String userId) async {
    try {
      final adminDoc = await _firestore
          .collection('admins')
          .doc(userId)
          .get();

      if (adminDoc.exists) {
        _currentAdmin.value = AdminUserModel.fromMap(userId, adminDoc.data()!);
        _isAuthenticated.value = _currentAdmin.value!.status == AdminStatus.active;
        
        // Update last login
        await _updateLastLogin(userId);
      } else {
        _isAuthenticated.value = false;
        _errorMessage.value = 'Admin access denied';
      }
    } catch (e) {
      debugPrint('Error loading admin data: $e');
      _isAuthenticated.value = false;
      _errorMessage.value = 'Failed to load admin data';
    }
  }

  Future<bool> signIn(String email, String password) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = null;

      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        await _loadAdminData(credential.user!.uid);
        return _isAuthenticated.value;
      }

      return false;
    } on FirebaseAuthException catch (e) {
      _errorMessage.value = _getAuthErrorMessage(e.code);
      return false;
    } catch (e) {
      _errorMessage.value = 'An unexpected error occurred';
      debugPrint('Admin sign in error: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> signOut() async {
    try {
      await _auth.signOut();
      _currentAdmin.value = null;
      _isAuthenticated.value = false;
      _errorMessage.value = null;
    } catch (e) {
      debugPrint('Error signing out: $e');
    }
  }

  Future<void> _updateLastLogin(String adminId) async {
    try {
      await _firestore
          .collection('admins')
          .doc(adminId)
          .update({
        'lastLogin': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error updating last login: $e');
    }
  }

  String _getAuthErrorMessage(String code) {
    switch (code) {
      case 'user-not-found':
        return 'Admin account not found';
      case 'wrong-password':
        return 'Invalid password';
      case 'invalid-email':
        return 'Invalid email address';
      case 'user-disabled':
        return 'Admin account has been disabled';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later';
      default:
        return 'Authentication failed';
    }
  }

  bool hasPermission(String permission) {
    return _currentAdmin.value?.hasPermission(permission) ?? false;
  }

  bool get canManageUsers => hasPermission('manage_users');
  bool get canManageWallets => hasPermission('manage_wallets');
  bool get canViewAnalytics => hasPermission('view_analytics');
  bool get canManageAdmins => _currentAdmin.value?.canManageAdmins ?? false;

  void clearError() {
    _errorMessage.value = null;
  }
}
