import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_management_model.dart';
import 'admin_auth_controller.dart';

class UserManagementController extends GetxController {
  static UserManagementController get to => Get.find();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Reactive variables
  final _users = <UserManagementModel>[].obs;
  final _filteredUsers = <UserManagementModel>[].obs;
  final _isLoading = false.obs;
  final _errorMessage = RxnString();
  final _searchQuery = ''.obs;
  final _selectedStatus = Rxn<UserStatus>();
  final _totalUsers = 0.obs;
  final _activeUsers = 0.obs;
  final _blockedUsers = 0.obs;

  StreamSubscription<QuerySnapshot>? _usersSubscription;

  // Getters
  List<UserManagementModel> get users => _filteredUsers;
  bool get isLoading => _isLoading.value;
  String? get errorMessage => _errorMessage.value;
  String get searchQuery => _searchQuery.value;
  UserStatus? get selectedStatus => _selectedStatus.value;
  int get totalUsers => _totalUsers.value;
  int get activeUsers => _activeUsers.value;
  int get blockedUsers => _blockedUsers.value;

  @override
  void onInit() {
    super.onInit();
    _initializeUsersStream();
    _setupSearchListener();
  }

  @override
  void onClose() {
    _usersSubscription?.cancel();
    super.onClose();
  }

  void _initializeUsersStream() {
    _isLoading.value = true;

    _usersSubscription = _firestore
        .collection('users')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .listen(
      (snapshot) {
        _processUsersSnapshot(snapshot);
        _isLoading.value = false;
      },
      onError: (error) {
        debugPrint('Error listening to users: $error');
        _errorMessage.value = 'Failed to load users';
        _isLoading.value = false;
      },
    );
  }

  void _processUsersSnapshot(QuerySnapshot snapshot) {
    final usersList = snapshot.docs.map((doc) {
      return UserManagementModel.fromMap(
          doc.id, doc.data() as Map<String, dynamic>);
    }).toList();

    _users.value = usersList;
    _updateStatistics();
    _applyFilters();
  }

  void _updateStatistics() {
    _totalUsers.value = _users.length;
    _activeUsers.value = _users.where((user) => user.isActive).length;
    _blockedUsers.value = _users.where((user) => user.isBlocked).length;
  }

  void _setupSearchListener() {
    debounce(_searchQuery, (_) => _applyFilters(),
        time: const Duration(milliseconds: 300));
    ever(_selectedStatus, (_) => _applyFilters());
  }

  void _applyFilters() {
    var filtered = _users.toList();

    // Apply search filter
    if (_searchQuery.value.isNotEmpty) {
      final query = _searchQuery.value.toLowerCase();
      filtered = filtered.where((user) {
        return user.email.toLowerCase().contains(query) ||
            (user.name?.toLowerCase().contains(query) ?? false) ||
            (user.username?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    // Apply status filter
    if (_selectedStatus.value != null) {
      filtered = filtered
          .where((user) => user.status == _selectedStatus.value)
          .toList();
    }

    _filteredUsers.value = filtered;
  }

  void setSearchQuery(String query) {
    _searchQuery.value = query;
  }

  void setStatusFilter(UserStatus? status) {
    _selectedStatus.value = status;
  }

  void clearFilters() {
    _searchQuery.value = '';
    _selectedStatus.value = null;
  }

  Future<bool> updateUserStatus(String userId, UserStatus newStatus) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'status': newStatus.name,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Log admin action
      await _logAdminAction(userId, 'Status changed to ${newStatus.name}');

      return true;
    } catch (e) {
      debugPrint('Error updating user status: $e');
      _errorMessage.value = 'Failed to update user status';
      return false;
    }
  }

  Future<bool> blockUser(String userId, String reason) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'status': UserStatus.blocked.name,
        'blockReason': reason,
        'blockedAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      await _logAdminAction(userId, 'User blocked: $reason');
      return true;
    } catch (e) {
      debugPrint('Error blocking user: $e');
      _errorMessage.value = 'Failed to block user';
      return false;
    }
  }

  Future<bool> unblockUser(String userId) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'status': UserStatus.active.name,
        'blockReason': FieldValue.delete(),
        'blockedAt': FieldValue.delete(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      await _logAdminAction(userId, 'User unblocked');
      return true;
    } catch (e) {
      debugPrint('Error unblocking user: $e');
      _errorMessage.value = 'Failed to unblock user';
      return false;
    }
  }

  Future<bool> updateUserProfile(
      String userId, Map<String, dynamic> updates) async {
    try {
      updates['updatedAt'] = FieldValue.serverTimestamp();

      await _firestore.collection('users').doc(userId).update(updates);

      await _logAdminAction(userId, 'Profile updated by admin');
      return true;
    } catch (e) {
      debugPrint('Error updating user profile: $e');
      _errorMessage.value = 'Failed to update user profile';
      return false;
    }
  }

  Future<void> _logAdminAction(String userId, String action) async {
    try {
      await _firestore.collection('admin_logs').add({
        'userId': userId,
        'action': action,
        'timestamp': FieldValue.serverTimestamp(),
        'adminId': Get.find<AdminAuthController>().currentAdmin?.id,
      });
    } catch (e) {
      debugPrint('Error logging admin action: $e');
    }
  }

  UserManagementModel? getUserById(String userId) {
    try {
      return _users.firstWhere((user) => user.id == userId);
    } catch (e) {
      return null;
    }
  }

  void clearError() {
    _errorMessage.value = null;
  }
}
