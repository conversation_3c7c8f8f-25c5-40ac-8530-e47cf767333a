import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/wallet_management_model.dart';
import '../../models/transaction_model.dart';
import 'admin_auth_controller.dart';

class WalletManagementController extends GetxController {
  static WalletManagementController get to => Get.find();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Reactive variables
  final _wallets = <WalletManagementModel>[].obs;
  final _filteredWallets = <WalletManagementModel>[].obs;
  final _transactions = <AdminTransactionModel>[].obs;
  final _userDetails = <String, Map<String, dynamic>>{}.obs;
  final _isLoading = false.obs;
  final _errorMessage = RxnString();
  final _searchQuery = ''.obs;
  final _selectedStatus = Rxn<WalletStatus>();
  final _totalWallets = 0.obs;
  final _totalBalance = 0.0.obs;
  final _blockedWallets = 0.obs;

  StreamSubscription<QuerySnapshot>? _walletsSubscription;
  StreamSubscription<QuerySnapshot>? _transactionsSubscription;

  // Getters
  List<WalletManagementModel> get wallets => _filteredWallets;
  List<AdminTransactionModel> get transactions => _transactions;
  Map<String, Map<String, dynamic>> get userDetails => _userDetails;
  bool get isLoading => _isLoading.value;
  String? get errorMessage => _errorMessage.value;
  String get searchQuery => _searchQuery.value;
  WalletStatus? get selectedStatus => _selectedStatus.value;
  int get totalWallets => _totalWallets.value;
  double get totalBalance => _totalBalance.value;
  int get blockedWallets => _blockedWallets.value;

  @override
  void onInit() {
    super.onInit();
    _initializeWalletsStream();
    _initializeTransactionsStream();
    _setupSearchListener();
  }

  @override
  void onClose() {
    _walletsSubscription?.cancel();
    _transactionsSubscription?.cancel();
    super.onClose();
  }

  void _initializeWalletsStream() {
    _isLoading.value = true;

    _walletsSubscription = _firestore.collection('wallets').snapshots().listen(
      (snapshot) {
        _processWalletsSnapshot(snapshot);
        _isLoading.value = false;
      },
      onError: (error) {
        debugPrint('Error listening to wallets: $error');
        _errorMessage.value = 'Failed to load wallets';
        _isLoading.value = false;
      },
    );
  }

  void _initializeTransactionsStream() {
    // Listen to all transactions across all wallets
    _transactionsSubscription = _firestore
        .collectionGroup('transactions')
        .orderBy('timestamp', descending: true)
        .limit(100) // Limit to recent transactions
        .snapshots()
        .listen(
      (snapshot) {
        _processTransactionsSnapshot(snapshot);
      },
      onError: (error) {
        debugPrint('Error listening to transactions: $error');
      },
    );
  }

  void _processWalletsSnapshot(QuerySnapshot snapshot) {
    final walletsList = snapshot.docs.map((doc) {
      return WalletManagementModel.fromMap(
          doc.id, doc.data() as Map<String, dynamic>);
    }).toList();

    _wallets.value = walletsList;
    _loadUserDetails();
    _updateStatistics();
    _applyFilters();
  }

  void _processTransactionsSnapshot(QuerySnapshot snapshot) {
    final transactionsList = snapshot.docs.map((doc) {
      final data = doc.data() as Map<String, dynamic>;
      // Extract userId from the document path
      final userId = doc.reference.parent.parent?.id ?? '';
      data['userId'] = userId;

      return AdminTransactionModel.fromMap(doc.id, data);
    }).toList();

    _transactions.value = transactionsList;
  }

  void _updateStatistics() {
    _totalWallets.value = _wallets.length;
    _totalBalance.value =
        _wallets.fold(0.0, (total, wallet) => total + wallet.balance);
    _blockedWallets.value = _wallets.where((wallet) => wallet.isBlocked).length;
  }

  void _loadUserDetails() async {
    for (final wallet in _wallets) {
      if (!_userDetails.containsKey(wallet.userId)) {
        try {
          final userDoc =
              await _firestore.collection('users').doc(wallet.userId).get();
          if (userDoc.exists) {
            _userDetails[wallet.userId] = userDoc.data()!;
          }
        } catch (e) {
          debugPrint('Error loading user details for ${wallet.userId}: $e');
        }
      }
    }
  }

  void _setupSearchListener() {
    debounce(_searchQuery, (_) => _applyFilters(),
        time: const Duration(milliseconds: 300));
    ever(_selectedStatus, (_) => _applyFilters());
  }

  void _applyFilters() {
    var filtered = _wallets.toList();

    // Apply search filter (by user ID, name, username, or email)
    if (_searchQuery.value.isNotEmpty) {
      final query = _searchQuery.value.toLowerCase();
      filtered = filtered.where((wallet) {
        final userDetail = _userDetails[wallet.userId];
        return wallet.userId.toLowerCase().contains(query) ||
            (userDetail?['name']?.toString().toLowerCase().contains(query) ??
                false) ||
            (userDetail?['username']
                    ?.toString()
                    .toLowerCase()
                    .contains(query) ??
                false) ||
            (userDetail?['email']?.toString().toLowerCase().contains(query) ??
                false);
      }).toList();
    }

    // Apply status filter
    if (_selectedStatus.value != null) {
      filtered = filtered
          .where((wallet) => wallet.status == _selectedStatus.value)
          .toList();
    }

    _filteredWallets.value = filtered;
  }

  void setSearchQuery(String query) {
    _searchQuery.value = query;
  }

  void setStatusFilter(WalletStatus? status) {
    _selectedStatus.value = status;
  }

  void clearFilters() {
    _searchQuery.value = '';
    _selectedStatus.value = null;
  }

  Future<bool> updateWalletStatus(String userId, WalletStatus newStatus,
      {String? reason}) async {
    try {
      final updateData = {
        'status': newStatus.name,
        'lastUpdated': FieldValue.serverTimestamp(),
      };

      if (reason != null) {
        updateData['statusReason'] = reason;
        updateData['statusChangedAt'] = FieldValue.serverTimestamp();
      }

      await _firestore.collection('wallets').doc(userId).update(updateData);

      // Log admin action
      await _logAdminAction(userId,
          'Wallet status changed to ${newStatus.name}${reason != null ? ': $reason' : ''}');

      return true;
    } catch (e) {
      debugPrint('Error updating wallet status: $e');
      _errorMessage.value = 'Failed to update wallet status';
      return false;
    }
  }

  Future<bool> blockWallet(String userId, String reason) async {
    return updateWalletStatus(userId, WalletStatus.blocked, reason: reason);
  }

  Future<bool> unblockWallet(String userId) async {
    try {
      await _firestore.collection('wallets').doc(userId).update({
        'status': WalletStatus.active.name,
        'statusReason': FieldValue.delete(),
        'statusChangedAt': FieldValue.delete(),
        'lastUpdated': FieldValue.serverTimestamp(),
      });

      await _logAdminAction(userId, 'Wallet unblocked');
      return true;
    } catch (e) {
      debugPrint('Error unblocking wallet: $e');
      _errorMessage.value = 'Failed to unblock wallet';
      return false;
    }
  }

  Future<bool> adjustWalletBalance(
      String userId, double amount, String reason) async {
    try {
      final walletRef = _firestore.collection('wallets').doc(userId);
      final transactionsRef = walletRef.collection('transactions');

      await _firestore.runTransaction((transaction) async {
        final walletDoc = await transaction.get(walletRef);

        if (!walletDoc.exists) {
          throw Exception('Wallet not found');
        }

        final currentBalance = (walletDoc.data()!['balance'] ?? 0.0).toDouble();
        final newBalance = currentBalance + amount;

        if (newBalance < 0) {
          throw Exception('Insufficient balance for adjustment');
        }

        // Update wallet balance
        transaction.update(walletRef, {
          'balance': newBalance,
          'lastUpdated': FieldValue.serverTimestamp(),
        });

        // Create admin transaction record
        final transactionDoc = transactionsRef.doc();
        transaction.set(transactionDoc, {
          'type': amount > 0
              ? TransactionType.credit.name
              : TransactionType.debit.name,
          'amount': amount.abs(),
          'description': 'Admin adjustment: $reason',
          'timestamp': FieldValue.serverTimestamp(),
          'status': TransactionStatus.completed.name,
          'userId': userId,
          'adminId': Get.find<AdminAuthController>().currentAdmin?.id,
          'adminNote': reason,
          'isAdminAction': true,
          'metadata': {
            'type': 'admin_adjustment',
            'previousBalance': currentBalance,
            'newBalance': newBalance,
          },
        });
      });

      await _logAdminAction(userId,
          'Wallet balance adjusted by \$${amount.toStringAsFixed(2)}: $reason');
      return true;
    } catch (e) {
      debugPrint('Error adjusting wallet balance: $e');
      _errorMessage.value = 'Failed to adjust wallet balance: ${e.toString()}';
      return false;
    }
  }

  Future<List<AdminTransactionModel>> getWalletTransactions(String userId,
      {int limit = 50}) async {
    try {
      final snapshot = await _firestore
          .collection('wallets')
          .doc(userId)
          .collection('transactions')
          .orderBy('timestamp', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        data['userId'] = userId;
        return AdminTransactionModel.fromMap(doc.id, data);
      }).toList();
    } catch (e) {
      debugPrint('Error getting wallet transactions: $e');
      return [];
    }
  }

  Future<void> _logAdminAction(String userId, String action) async {
    try {
      await _firestore.collection('admin_logs').add({
        'userId': userId,
        'action': action,
        'timestamp': FieldValue.serverTimestamp(),
        'adminId': Get.find<AdminAuthController>().currentAdmin?.id,
        'type': 'wallet_management',
      });
    } catch (e) {
      debugPrint('Error logging admin action: $e');
    }
  }

  WalletManagementModel? getWalletByUserId(String userId) {
    try {
      return _wallets.firstWhere((wallet) => wallet.userId == userId);
    } catch (e) {
      return null;
    }
  }

  Map<String, dynamic>? getUserDetails(String userId) {
    return _userDetails[userId];
  }

  String getUserDisplayName(String userId) {
    final userDetail = _userDetails[userId];
    if (userDetail != null) {
      final name = userDetail['name']?.toString();
      final username = userDetail['username']?.toString();
      final email = userDetail['email']?.toString();

      if (name != null && name.isNotEmpty) {
        return name;
      } else if (username != null && username.isNotEmpty) {
        return '@$username';
      } else if (email != null && email.isNotEmpty) {
        return email;
      }
    }
    return userId;
  }

  void clearError() {
    _errorMessage.value = null;
  }
}
