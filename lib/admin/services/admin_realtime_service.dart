import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class AdminRealtimeService extends GetxService {
  static AdminRealtimeService get to => Get.find();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Stream subscriptions
  final Map<String, StreamSubscription> _subscriptions = {};

  // Real-time data observables
  final _totalUsers = 0.obs;
  final _totalWallets = 0.obs;
  final _totalBalance = 0.0.obs;
  final _recentActivities = <Map<String, dynamic>>[].obs;
  final _systemStats = <String, dynamic>{}.obs;

  // Getters
  int get totalUsers => _totalUsers.value;
  int get totalWallets => _totalWallets.value;
  double get totalBalance => _totalBalance.value;
  List<Map<String, dynamic>> get recentActivities => _recentActivities;
  Map<String, dynamic> get systemStats => _systemStats;

  @override
  void onInit() {
    super.onInit();
    _initializeRealtimeStreams();
  }

  @override
  void onClose() {
    _disposeAllStreams();
    super.onClose();
  }

  void _initializeRealtimeStreams() {
    _setupUsersStream();
    _setupWalletsStream();
    _setupActivitiesStream();
    _setupSystemStatsStream();
  }

  void _setupUsersStream() {
    _subscriptions['users'] = _firestore.collection('users').snapshots().listen(
      (snapshot) {
        _totalUsers.value = snapshot.docs.length;
        _updateSystemStats('totalUsers', snapshot.docs.length);

        // Calculate user statistics
        final activeUsers = snapshot.docs.where((doc) {
          final data = doc.data();
          return data['status'] != 'blocked' && data['status'] != 'deleted';
        }).length;

        _updateSystemStats('activeUsers', activeUsers);

        debugPrint(
            'AdminRealtimeService: Users updated - Total: ${snapshot.docs.length}');
      },
      onError: (error) {
        debugPrint('AdminRealtimeService: Error in users stream: $error');
      },
    );
  }

  void _setupWalletsStream() {
    _subscriptions['wallets'] =
        _firestore.collection('wallets').snapshots().listen(
      (snapshot) {
        _totalWallets.value = snapshot.docs.length;

        // Calculate total balance
        double totalBalance = 0.0;
        for (final doc in snapshot.docs) {
          final data = doc.data();
          totalBalance += (data['balance'] ?? 0.0).toDouble();
        }
        _totalBalance.value = totalBalance;

        _updateSystemStats('totalWallets', snapshot.docs.length);
        _updateSystemStats('totalBalance', totalBalance);

        debugPrint(
            'AdminRealtimeService: Wallets updated - Total: ${snapshot.docs.length}, Balance: \$${totalBalance.toStringAsFixed(2)}');
      },
      onError: (error) {
        debugPrint('AdminRealtimeService: Error in wallets stream: $error');
      },
    );
  }

  void _setupActivitiesStream() {
    _subscriptions['activities'] = _firestore
        .collection('user_activities')
        .orderBy('timestamp', descending: true)
        .limit(10)
        .snapshots()
        .listen(
      (snapshot) {
        final activities = snapshot.docs.map((doc) {
          final data = doc.data();
          return {
            'id': doc.id,
            'userId': data['userId'] ?? '',
            'type': data['type'] ?? '',
            'description': data['description'] ?? '',
            'timestamp': data['timestamp'],
          };
        }).toList();

        _recentActivities.value = activities;

        debugPrint(
            'AdminRealtimeService: Recent activities updated - Count: ${activities.length}');
      },
      onError: (error) {
        debugPrint('AdminRealtimeService: Error in activities stream: $error');
      },
    );
  }

  void _setupSystemStatsStream() {
    // Update system stats every minute
    Timer.periodic(const Duration(minutes: 1), (timer) {
      _updateSystemStats('lastUpdated', DateTime.now().millisecondsSinceEpoch);
    });
  }

  void _updateSystemStats(String key, dynamic value) {
    final currentStats = Map<String, dynamic>.from(_systemStats);
    currentStats[key] = value;
    _systemStats.value = currentStats;
  }

  // Public methods for manual data refresh
  Future<void> refreshUserData() async {
    try {
      final snapshot = await _firestore.collection('users').get();
      _totalUsers.value = snapshot.docs.length;
      _updateSystemStats('totalUsers', snapshot.docs.length);
    } catch (e) {
      debugPrint('AdminRealtimeService: Error refreshing user data: $e');
    }
  }

  Future<void> refreshWalletData() async {
    try {
      final snapshot = await _firestore.collection('wallets').get();
      _totalWallets.value = snapshot.docs.length;

      double totalBalance = 0.0;
      for (final doc in snapshot.docs) {
        final data = doc.data();
        totalBalance += (data['balance'] ?? 0.0).toDouble();
      }
      _totalBalance.value = totalBalance;

      _updateSystemStats('totalWallets', snapshot.docs.length);
      _updateSystemStats('totalBalance', totalBalance);
    } catch (e) {
      debugPrint('AdminRealtimeService: Error refreshing wallet data: $e');
    }
  }

  Future<Map<String, int>> getHourlyActivityStats() async {
    try {
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final snapshot = await _firestore
          .collection('user_activities')
          .where('timestamp',
              isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .where('timestamp', isLessThan: Timestamp.fromDate(endOfDay))
          .get();

      final hourlyStats = <String, int>{};

      // Initialize all hours
      for (int i = 0; i < 24; i++) {
        hourlyStats[i.toString().padLeft(2, '0')] = 0;
      }

      for (final doc in snapshot.docs) {
        final data = doc.data();
        final timestamp = (data['timestamp'] as Timestamp).toDate();
        final hour = timestamp.hour.toString().padLeft(2, '0');
        hourlyStats[hour] = (hourlyStats[hour] ?? 0) + 1;
      }

      return hourlyStats;
    } catch (e) {
      debugPrint('AdminRealtimeService: Error getting hourly stats: $e');
      return {};
    }
  }

  Future<Map<String, int>> getActivityTypeStats() async {
    try {
      final snapshot = await _firestore
          .collection('user_activities')
          .where('timestamp',
              isGreaterThan: Timestamp.fromDate(
                  DateTime.now().subtract(const Duration(days: 7))))
          .get();

      final typeStats = <String, int>{};

      for (final doc in snapshot.docs) {
        final data = doc.data();
        final type = data['type'] as String;
        typeStats[type] = (typeStats[type] ?? 0) + 1;
      }

      return typeStats;
    } catch (e) {
      debugPrint('AdminRealtimeService: Error getting activity type stats: $e');
      return {};
    }
  }

  // Stream management
  void pauseStream(String streamName) {
    _subscriptions[streamName]?.pause();
  }

  void resumeStream(String streamName) {
    _subscriptions[streamName]?.resume();
  }

  void _disposeAllStreams() {
    for (final subscription in _subscriptions.values) {
      subscription.cancel();
    }
    _subscriptions.clear();
  }

  // Health check
  bool get isHealthy {
    return _subscriptions.isNotEmpty &&
        _subscriptions.values.every((sub) => !sub.isPaused);
  }

  Map<String, dynamic> getHealthStatus() {
    return {
      'isHealthy': isHealthy,
      'activeStreams': _subscriptions.length,
      'streamStatus': _subscriptions
          .map((key, value) => MapEntry(key, {'isPaused': value.isPaused})),
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }
}
