import 'package:flutter/material.dart';
import '../theme/admin_theme.dart';

class DashboardStatsCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  final String? trend;
  final String? subtitle;

  const DashboardStatsCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    this.trend,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AdminTheme.spacingLarge),
      decoration: AdminTheme.cardDecoration(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(AdminTheme.spacingMedium),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 28,
                ),
              ),
              if (trend != null)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AdminTheme.spacingMedium,
                    vertical: AdminTheme.spacingSmall,
                  ),
                  decoration: BoxDecoration(
                    color: _getTrendColor().withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AdminTheme.radiusLarge),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getTrendIcon(),
                        color: _getTrendColor(),
                        size: 16,
                      ),
                      const SizedBox(width: AdminTheme.spacingXSmall),
                      Text(
                        trend!,
                        style: AdminTheme.labelMedium.copyWith(
                          color: _getTrendColor(),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
          const SizedBox(height: AdminTheme.spacingLarge),
          Text(
            value,
            style: AdminTheme.headingLarge.copyWith(
              fontSize: 32,
              fontWeight: FontWeight.w800,
            ),
          ),
          const SizedBox(height: AdminTheme.spacingXSmall),
          Text(
            title,
            style: AdminTheme.bodyMedium.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: AdminTheme.spacingXSmall),
            Text(
              subtitle!,
              style: AdminTheme.bodySmall,
            ),
          ],
        ],
      ),
    );
  }

  Color _getTrendColor() {
    if (trend == null) return Colors.grey;

    if (trend!.startsWith('+')) {
      return Colors.green;
    } else if (trend!.startsWith('-')) {
      return Colors.red;
    }
    return Colors.grey;
  }

  IconData _getTrendIcon() {
    if (trend == null) return Icons.trending_flat;

    if (trend!.startsWith('+')) {
      return Icons.trending_up;
    } else if (trend!.startsWith('-')) {
      return Icons.trending_down;
    }
    return Icons.trending_flat;
  }
}
