import 'package:flutter/material.dart';
import '../theme/admin_theme.dart';

class DashboardStatsCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  final String? trend;
  final String? subtitle;

  const DashboardStatsCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    this.trend,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;

    return Container(
      padding: EdgeInsets.all(
        isMobile ? AdminTheme.spacingMedium : AdminTheme.spacingLarge,
      ),
      decoration: AdminTheme.cardDecoration(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: EdgeInsets.all(
                  isMobile ? AdminTheme.spacingSmall : AdminTheme.spacingMedium,
                ),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: isMobile ? 20 : 28,
                ),
              ),
              if (trend != null)
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: isMobile
                        ? AdminTheme.spacingSmall
                        : AdminTheme.spacingMedium,
                    vertical: AdminTheme.spacingXSmall,
                  ),
                  decoration: BoxDecoration(
                    color: _getTrendColor().withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AdminTheme.radiusLarge),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getTrendIcon(),
                        color: _getTrendColor(),
                        size: isMobile ? 12 : 16,
                      ),
                      SizedBox(width: isMobile ? 2 : AdminTheme.spacingXSmall),
                      Text(
                        trend!,
                        style: (isMobile
                                ? AdminTheme.bodySmall
                                : AdminTheme.labelMedium)
                            .copyWith(
                          color: _getTrendColor(),
                          fontWeight: FontWeight.w600,
                          fontSize: isMobile ? 10 : 12,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
          SizedBox(
              height: isMobile
                  ? AdminTheme.spacingMedium
                  : AdminTheme.spacingLarge),
          Text(
            value,
            style: AdminTheme.headingLarge.copyWith(
              fontSize: isMobile ? 24 : 32,
              fontWeight: FontWeight.w800,
              color: AdminTheme.textPrimary,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: isMobile ? 2 : AdminTheme.spacingXSmall),
          Text(
            title,
            style: (isMobile ? AdminTheme.bodySmall : AdminTheme.bodyMedium)
                .copyWith(
              fontWeight: FontWeight.w500,
              color: AdminTheme.textSecondary,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          if (subtitle != null) ...[
            SizedBox(height: isMobile ? 2 : AdminTheme.spacingXSmall),
            Text(
              subtitle!,
              style: AdminTheme.bodySmall.copyWith(
                fontSize: isMobile ? 10 : 12,
                color: AdminTheme.textSecondary,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }

  Color _getTrendColor() {
    if (trend == null) return Colors.grey;

    if (trend!.startsWith('+')) {
      return Colors.green;
    } else if (trend!.startsWith('-')) {
      return Colors.red;
    }
    return Colors.grey;
  }

  IconData _getTrendIcon() {
    if (trend == null) return Icons.trending_flat;

    if (trend!.startsWith('+')) {
      return Icons.trending_up;
    } else if (trend!.startsWith('-')) {
      return Icons.trending_down;
    }
    return Icons.trending_flat;
  }
}
