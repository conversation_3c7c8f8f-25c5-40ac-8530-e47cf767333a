import 'package:flutter/material.dart';
import '../theme/admin_theme.dart';
import 'admin_sidebar.dart';

class ResponsiveLayout extends StatelessWidget {
  final Widget child;
  final String title;
  final Widget? floatingActionButton;

  const ResponsiveLayout({
    super.key,
    required this.child,
    required this.title,
    this.floatingActionButton,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;
    final isTablet = screenWidth >= 768 && screenWidth < 1024;

    if (isMobile) {
      return _buildMobileLayout(context);
    } else {
      return _buildDesktopLayout(context, isTablet);
    }
  }

  Widget _buildMobileLayout(BuildContext context) {
    return Scaffold(
      backgroundColor: AdminTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          title,
          style: AdminTheme.headingSmall.copyWith(
            color: AdminTheme.textOnPrimary,
          ),
        ),
        backgroundColor: AdminTheme.primaryColor,
        foregroundColor: AdminTheme.textOnPrimary,
        elevation: 0,
      ),
      drawer: Drawer(
        child: _buildMobileDrawer(context),
      ),
      body: Padding(
        padding: const EdgeInsets.all(AdminTheme.spacingMedium),
        child: child,
      ),
      floatingActionButton: floatingActionButton,
    );
  }

  Widget _buildDesktopLayout(BuildContext context, bool isTablet) {
    return Scaffold(
      backgroundColor: AdminTheme.backgroundColor,
      body: Row(
        children: [
          const AdminSidebar(),
          Expanded(
            child: Column(
              children: [
                _buildTopAppBar(context, isTablet),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.all(
                      isTablet ? AdminTheme.spacingMedium : AdminTheme.spacingLarge,
                    ),
                    child: child,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: floatingActionButton,
    );
  }

  Widget _buildTopAppBar(BuildContext context, bool isTablet) {
    return Container(
      height: 70,
      decoration: BoxDecoration(
        color: AdminTheme.surfaceColor,
        boxShadow: AdminTheme.cardShadow,
      ),
      padding: EdgeInsets.symmetric(
        horizontal: isTablet ? AdminTheme.spacingMedium : AdminTheme.spacingLarge,
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: AdminTheme.headingSmall,
            ),
          ),
          if (floatingActionButton != null) floatingActionButton!,
        ],
      ),
    );
  }

  Widget _buildMobileDrawer(BuildContext context) {
    return Container(
      color: AdminTheme.surfaceColor,
      child: Column(
        children: [
          // Logo Section
          Container(
            height: 120,
            padding: const EdgeInsets.all(AdminTheme.spacingLarge),
            decoration: BoxDecoration(
              color: AdminTheme.primaryColor,
            ),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AdminTheme.textOnPrimary,
                    borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
                  ),
                  child: Icon(
                    Icons.admin_panel_settings,
                    color: AdminTheme.primaryColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: AdminTheme.spacingMedium),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Money Mouthy',
                      style: AdminTheme.headingSmall.copyWith(
                        color: AdminTheme.textOnPrimary,
                      ),
                    ),
                    Text(
                      'Admin Panel',
                      style: AdminTheme.bodySmall.copyWith(
                        color: AdminTheme.textOnPrimary.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Navigation Menu
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(vertical: AdminTheme.spacingMedium),
              children: [
                _buildMobileMenuItem(
                  icon: Icons.dashboard,
                  title: 'Dashboard',
                  route: '/admin/dashboard',
                  context: context,
                ),
                _buildMobileMenuItem(
                  icon: Icons.people,
                  title: 'User Management',
                  route: '/admin/users',
                  context: context,
                ),
                _buildMobileMenuItem(
                  icon: Icons.account_balance_wallet,
                  title: 'Wallet Management',
                  route: '/admin/wallets',
                  context: context,
                ),
                _buildMobileMenuItem(
                  icon: Icons.timeline,
                  title: 'User Activity',
                  route: '/admin/activity',
                  context: context,
                ),
              ],
            ),
          ),

          // Logout Section
          Container(
            padding: const EdgeInsets.all(AdminTheme.spacingLarge),
            decoration: const BoxDecoration(
              border: Border(
                top: BorderSide(color: AdminTheme.borderColor),
              ),
            ),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  _showLogoutDialog(context);
                },
                icon: const Icon(Icons.logout),
                label: const Text('Logout'),
                style: AdminTheme.errorButtonStyle,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileMenuItem({
    required IconData icon,
    required String title,
    required String route,
    required BuildContext context,
  }) {
    final isSelected = ModalRoute.of(context)?.settings.name == route;
    
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AdminTheme.spacingMedium,
        vertical: AdminTheme.spacingXSmall,
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isSelected ? AdminTheme.primaryColor : AdminTheme.textSecondary,
        ),
        title: Text(
          title,
          style: AdminTheme.bodyLarge.copyWith(
            color: isSelected ? AdminTheme.primaryColor : AdminTheme.textPrimary,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
        selected: isSelected,
        selectedTileColor: AdminTheme.primaryColor.withOpacity(0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
        ),
        onTap: () {
          Navigator.of(context).pop();
          if (!isSelected) {
            Navigator.of(context).pushReplacementNamed(route);
          }
        },
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Add logout logic here
            },
            style: AdminTheme.errorButtonStyle,
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}
