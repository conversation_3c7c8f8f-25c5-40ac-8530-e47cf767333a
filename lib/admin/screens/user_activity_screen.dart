import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/user_activity_controller.dart';
import '../models/user_management_model.dart';
import '../theme/admin_theme.dart';
import '../widgets/mobile_admin_layout.dart';
import '../widgets/activity_type_chip.dart';

class UserActivityScreen extends StatefulWidget {
  const UserActivityScreen({super.key});

  @override
  State<UserActivityScreen> createState() => _UserActivityScreenState();
}

class _UserActivityScreenState extends State<UserActivityScreen> {
  final _activityController = Get.put(UserActivityController());
  final _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MobileAdminLayout(
      title: 'User Activity',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Head<PERSON> and Stats
          _buildHeaderSection(),
          const SizedBox(height: AdminTheme.spacingLarge),

          // Filters and Search
          _buildFiltersSection(),
          const SizedBox(height: AdminTheme.spacingLarge),

          // Activity List
          Expanded(child: _buildActivityList()),
        ],
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Obx(() {
      final screenWidth = MediaQuery.of(context).size.width;
      final isMobile = screenWidth < 768;

      if (isMobile) {
        return Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Activities',
                    _activityController.totalActivities.toString(),
                    Icons.timeline,
                    AdminTheme.primaryColor,
                  ),
                ),
                const SizedBox(width: AdminTheme.spacingMedium),
                Expanded(
                  child: _buildStatCard(
                    'Today\'s Activities',
                    _activityController.todayActivities.toString(),
                    Icons.today,
                    AdminTheme.successColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AdminTheme.spacingSmall),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Online Users',
                    _activityController.onlineUsers.toString(),
                    Icons.people,
                    AdminTheme.warningColor,
                  ),
                ),
                const SizedBox(width: AdminTheme.spacingMedium),
                Expanded(
                  child: _buildStatCard(
                    'Peak Hour',
                    _calculatePeakHour(),
                    Icons.schedule,
                    AdminTheme.errorColor,
                  ),
                ),
              ],
            ),
          ],
        );
      } else {
        return Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Total Activities',
                _activityController.totalActivities.toString(),
                Icons.timeline,
                AdminTheme.primaryColor,
              ),
            ),
            const SizedBox(width: AdminTheme.spacingMedium),
            Expanded(
              child: _buildStatCard(
                'Today\'s Activities',
                _activityController.todayActivities.toString(),
                Icons.today,
                AdminTheme.successColor,
              ),
            ),
            const SizedBox(width: AdminTheme.spacingMedium),
            Expanded(
              child: _buildStatCard(
                'Online Users',
                _activityController.onlineUsers.toString(),
                Icons.people,
                AdminTheme.warningColor,
              ),
            ),
            const SizedBox(width: AdminTheme.spacingMedium),
            Expanded(
              child: _buildStatCard(
                'Peak Hour',
                _calculatePeakHour(),
                Icons.schedule,
                AdminTheme.errorColor,
              ),
            ),
          ],
        );
      }
    });
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;

    return Container(
      padding: EdgeInsets.all(
          isMobile ? AdminTheme.spacingMedium : AdminTheme.spacingLarge),
      decoration: BoxDecoration(
        color: AdminTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
        boxShadow: AdminTheme.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(isMobile ? 8 : 12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AdminTheme.radiusSmall),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: isMobile ? 20 : 24,
                ),
              ),
              const Spacer(),
              if (!isMobile)
                Icon(
                  Icons.trending_up,
                  color: AdminTheme.textSecondary,
                  size: 16,
                ),
            ],
          ),
          SizedBox(
              height: isMobile
                  ? AdminTheme.spacingSmall
                  : AdminTheme.spacingMedium),
          Text(
            value,
            style: AdminTheme.headingMedium.copyWith(
              fontSize: isMobile ? 20 : 24,
              fontWeight: FontWeight.w700,
              color: AdminTheme.textPrimary,
            ),
          ),
          const SizedBox(height: AdminTheme.spacingXSmall),
          Text(
            title,
            style: AdminTheme.bodySmall.copyWith(
              color: AdminTheme.textSecondary,
              fontSize: isMobile ? 12 : 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;

    return Container(
      padding: EdgeInsets.all(
          isMobile ? AdminTheme.spacingMedium : AdminTheme.spacingLarge),
      decoration: BoxDecoration(
        color: AdminTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
        boxShadow: AdminTheme.cardShadow,
      ),
      child: isMobile
          ? _buildMobileActivityFilters()
          : _buildDesktopActivityFilters(),
    );
  }

  Widget _buildMobileActivityFilters() {
    return Column(
      children: [
        // Search Field
        TextField(
          controller: _searchController,
          decoration: AdminTheme.inputDecoration(
            labelText: 'Search activities...',
            hintText: 'User name, ID, email, or description',
            prefixIcon: const Icon(Icons.search),
          ),
          onChanged: _activityController.setSearchQuery,
        ),
        const SizedBox(height: AdminTheme.spacingSmall),

        // Activity Type Filter
        Obx(() => DropdownButtonFormField<UserActivityType?>(
              value: _activityController.selectedActivityType,
              decoration: AdminTheme.inputDecoration(
                labelText: 'Activity Type',
                prefixIcon: const Icon(Icons.filter_list),
              ),
              items: [
                const DropdownMenuItem<UserActivityType?>(
                  value: null,
                  child: Text('All Types'),
                ),
                ...UserActivityType.values.map((type) => DropdownMenuItem(
                      value: type,
                      child: Text(type.name.toUpperCase()),
                    )),
              ],
              onChanged: _activityController.setActivityTypeFilter,
            )),
        const SizedBox(height: AdminTheme.spacingSmall),

        // Date Range and Clear Button Row
        Row(
          children: [
            Expanded(
              flex: 2,
              child: Obx(() => OutlinedButton.icon(
                    onPressed: _showDateRangePicker,
                    icon: const Icon(Icons.date_range, size: 18),
                    label: Text(
                      _activityController.selectedDateRange != null
                          ? '${_formatDate(_activityController.selectedDateRange!.start)} - ${_formatDate(_activityController.selectedDateRange!.end)}'
                          : 'Date Range',
                      style: AdminTheme.bodySmall,
                    ),
                    style: AdminTheme.secondaryButtonStyle,
                  )),
            ),
            const SizedBox(width: AdminTheme.spacingMedium),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  _searchController.clear();
                  _activityController.clearFilters();
                },
                icon: const Icon(Icons.clear, size: 18),
                label: const Text('Clear'),
                style: AdminTheme.secondaryButtonStyle,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDesktopActivityFilters() {
    return Row(
      children: [
        // Search Field
        Expanded(
          flex: 2,
          child: TextField(
            controller: _searchController,
            decoration: AdminTheme.inputDecoration(
              labelText: 'Search activities...',
              hintText: 'Search by user name, ID, email, or description',
              prefixIcon: const Icon(Icons.search),
            ),
            onChanged: _activityController.setSearchQuery,
          ),
        ),
        const SizedBox(width: AdminTheme.spacingMedium),

        // Activity Type Filter
        Expanded(
          child: Obx(() => DropdownButtonFormField<UserActivityType?>(
                value: _activityController.selectedActivityType,
                decoration: AdminTheme.inputDecoration(
                  labelText: 'Filter by Type',
                  prefixIcon: const Icon(Icons.filter_list),
                ),
                items: [
                  const DropdownMenuItem<UserActivityType?>(
                    value: null,
                    child: Text('All Types'),
                  ),
                  ...UserActivityType.values.map((type) => DropdownMenuItem(
                        value: type,
                        child: Text(type.name.toUpperCase()),
                      )),
                ],
                onChanged: _activityController.setActivityTypeFilter,
              )),
        ),
        const SizedBox(width: AdminTheme.spacingMedium),

        // Date Range Filter
        Expanded(
          child: Obx(() => OutlinedButton.icon(
                onPressed: _showDateRangePicker,
                icon: const Icon(Icons.date_range),
                label: Text(
                  _activityController.selectedDateRange != null
                      ? '${_formatDate(_activityController.selectedDateRange!.start)} - ${_formatDate(_activityController.selectedDateRange!.end)}'
                      : 'Date Range',
                ),
                style: AdminTheme.secondaryButtonStyle,
              )),
        ),
        const SizedBox(width: AdminTheme.spacingMedium),

        // Clear Filters Button
        ElevatedButton.icon(
          onPressed: () {
            _searchController.clear();
            _activityController.clearFilters();
          },
          icon: const Icon(Icons.clear),
          label: const Text('Clear'),
          style: AdminTheme.secondaryButtonStyle,
        ),
      ],
    );
  }

  Widget _buildActivityList() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;

    return Container(
      decoration: BoxDecoration(
        color: AdminTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
        boxShadow: AdminTheme.cardShadow,
      ),
      child: Column(
        children: [
          // List Header (only for desktop)
          if (!isMobile)
            Container(
              padding: const EdgeInsets.all(AdminTheme.spacingMedium),
              decoration: BoxDecoration(
                color: AdminTheme.backgroundColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AdminTheme.radiusMedium),
                  topRight: Radius.circular(AdminTheme.radiusMedium),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      'Activity',
                      style: AdminTheme.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AdminTheme.textPrimary,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      'User',
                      style: AdminTheme.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AdminTheme.textPrimary,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      'Type',
                      style: AdminTheme.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AdminTheme.textPrimary,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      'Timestamp',
                      style: AdminTheme.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AdminTheme.textPrimary,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      'IP Address',
                      style: AdminTheme.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AdminTheme.textPrimary,
                      ),
                    ),
                  ),
                ],
              ),
            ),

          // Activity List
          Expanded(
            child: Obx(() {
              if (_activityController.isLoading) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(
                        color: AdminTheme.primaryColor,
                      ),
                      const SizedBox(height: AdminTheme.spacingMedium),
                      Text(
                        'Loading activities...',
                        style: AdminTheme.bodyMedium.copyWith(
                          color: AdminTheme.textSecondary,
                        ),
                      ),
                    ],
                  ),
                );
              }

              if (_activityController.activities.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.timeline,
                        size: 64,
                        color: AdminTheme.textSecondary,
                      ),
                      const SizedBox(height: AdminTheme.spacingMedium),
                      Text(
                        'No activities found',
                        style: AdminTheme.headingSmall.copyWith(
                          color: AdminTheme.textSecondary,
                        ),
                      ),
                      const SizedBox(height: AdminTheme.spacingSmall),
                      Text(
                        'Try adjusting your search or filters',
                        style: AdminTheme.bodyMedium.copyWith(
                          color: AdminTheme.textSecondary,
                        ),
                      ),
                    ],
                  ),
                );
              }

              return ListView.builder(
                padding: EdgeInsets.all(isMobile ? AdminTheme.spacingSmall : 0),
                itemCount: _activityController.activities.length,
                itemBuilder: (context, index) {
                  final activity = _activityController.activities[index];
                  return isMobile
                      ? _buildMobileActivityCard(activity)
                      : _buildDesktopActivityRow(activity);
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileActivityCard(UserActivityModel activity) {
    return Container(
      margin: const EdgeInsets.only(bottom: AdminTheme.spacingSmall),
      padding: const EdgeInsets.all(AdminTheme.spacingSmall),
      decoration: BoxDecoration(
        color: AdminTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AdminTheme.radiusSmall),
        boxShadow: AdminTheme.cardShadow,
        border: Border.all(color: AdminTheme.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Activity Header
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: AdminTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AdminTheme.radiusSmall),
                ),
                child: Icon(
                  Icons.timeline,
                  color: AdminTheme.primaryColor,
                  size: 16,
                ),
              ),
              const SizedBox(width: AdminTheme.spacingSmall),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      activity.description,
                      style: AdminTheme.bodySmall.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AdminTheme.textPrimary,
                        fontSize: 13,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      _formatDateTime(activity.timestamp),
                      style: AdminTheme.bodySmall.copyWith(
                        color: AdminTheme.textSecondary,
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: AdminTheme.spacingSmall),
              Flexible(
                child: ActivityTypeChip(type: activity.type),
              ),
            ],
          ),

          const SizedBox(height: AdminTheme.spacingSmall),

          // User and IP Info
          Row(
            children: [
              Expanded(
                child: _buildActivityInfoItem(
                  'User',
                  _activityController.getUserDisplayName(activity.userId),
                  Icons.person,
                ),
              ),
              const SizedBox(width: AdminTheme.spacingSmall),
              Expanded(
                child: _buildActivityInfoItem(
                  'IP',
                  activity.ipAddress ?? 'N/A',
                  Icons.location_on,
                ),
              ),
            ],
          ),

          if (activity.metadata != null) ...[
            const SizedBox(height: AdminTheme.spacingSmall),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AdminTheme.spacingSmall,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: AdminTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AdminTheme.radiusSmall),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.info_outline,
                    color: AdminTheme.primaryColor,
                    size: 12,
                  ),
                  const SizedBox(width: 4),
                  Flexible(
                    child: Text(
                      'Metadata available',
                      style: AdminTheme.bodySmall.copyWith(
                        color: AdminTheme.primaryColor,
                        fontWeight: FontWeight.w500,
                        fontSize: 10,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActivityInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 12,
          color: AdminTheme.textSecondary,
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                label,
                style: AdminTheme.bodySmall.copyWith(
                  color: AdminTheme.textSecondary,
                  fontSize: 9,
                ),
              ),
              Text(
                value,
                style: AdminTheme.bodySmall.copyWith(
                  color: AdminTheme.textPrimary,
                  fontWeight: FontWeight.w500,
                  fontSize: 11,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDesktopActivityRow(UserActivityModel activity) {
    return Container(
      padding: const EdgeInsets.all(AdminTheme.spacingMedium),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AdminTheme.borderColor),
        ),
      ),
      child: Row(
        children: [
          // Activity Description
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity.description,
                  style: AdminTheme.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AdminTheme.textPrimary,
                  ),
                ),
                if (activity.metadata != null) ...[
                  const SizedBox(height: AdminTheme.spacingXSmall),
                  Text(
                    'Additional info available',
                    style: AdminTheme.bodySmall.copyWith(
                      color: AdminTheme.textSecondary,
                    ),
                  ),
                ],
              ],
            ),
          ),

          // User Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _activityController.getUserDisplayName(activity.userId),
                  style: AdminTheme.bodySmall.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AdminTheme.textPrimary,
                  ),
                ),
                Text(
                  activity.userId,
                  style: AdminTheme.bodySmall.copyWith(
                    fontFamily: 'monospace',
                    color: AdminTheme.textSecondary,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),

          // Activity Type
          Expanded(
            child: ActivityTypeChip(type: activity.type),
          ),

          // Timestamp
          Expanded(
            child: Text(
              _formatDateTime(activity.timestamp),
              style: AdminTheme.bodySmall.copyWith(
                color: AdminTheme.textPrimary,
              ),
            ),
          ),

          // IP Address
          Expanded(
            child: Text(
              activity.ipAddress ?? 'N/A',
              style: AdminTheme.bodySmall.copyWith(
                fontFamily: 'monospace',
                color: AdminTheme.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showDateRangePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: _activityController.selectedDateRange,
    );

    if (picked != null) {
      _activityController.setDateRangeFilter(picked);
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _calculatePeakHour() {
    if (_activityController.activities.isEmpty) return 'N/A';

    final hourCounts = <int, int>{};
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);

    // Count activities by hour for today
    for (final activity in _activityController.activities) {
      if (activity.timestamp.isAfter(startOfDay)) {
        final hour = activity.timestamp.hour;
        hourCounts[hour] = (hourCounts[hour] ?? 0) + 1;
      }
    }

    if (hourCounts.isEmpty) return 'N/A';

    // Find the hour with most activities
    final peakHour =
        hourCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key;
    return '${peakHour.toString().padLeft(2, '0')}:00';
  }
}
