import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/user_activity_controller.dart';
import '../models/user_management_model.dart';
import '../widgets/admin_sidebar.dart';
import '../widgets/activity_type_chip.dart';

class UserActivityScreen extends StatefulWidget {
  const UserActivityScreen({super.key});

  @override
  State<UserActivityScreen> createState() => _UserActivityScreenState();
}

class _UserActivityScreenState extends State<UserActivityScreen> {
  final _activityController = Get.put(UserActivityController());
  final _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Row(
        children: [
          // Sidebar
          const AdminSidebar(),

          // Main Content
          Expanded(
            child: Column(
              children: [
                // Top App Bar
                _buildTopAppBar(),

                // Content
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header and Stats
                        _buildHeaderSection(),
                        const SizedBox(height: 24),

                        // Filters and Search
                        _buildFiltersSection(),
                        const SizedBox(height: 24),

                        // Activity List
                        Expanded(child: _buildActivityList()),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopAppBar() {
    return Container(
      height: 70,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: Row(
          children: [
            Text(
              'User Activity',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
            ),
            const Spacer(),

            // Real-time indicator
            Obx(() => Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: Colors.green.withOpacity(0.3)),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'Live (${_activityController.onlineUsers} online)',
                        style: const TextStyle(
                          color: Colors.green,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                )),
            const SizedBox(width: 16),

            // Refresh Button
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                // Refresh is automatic with streams
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Obx(() => Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Total Activities',
                _activityController.totalActivities.toString(),
                Icons.timeline,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'Today\'s Activities',
                _activityController.todayActivities.toString(),
                Icons.today,
                Colors.green,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'Online Users',
                _activityController.onlineUsers.toString(),
                Icons.people,
                Colors.orange,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'Peak Hour',
                _calculatePeakHour(),
                Icons.schedule,
                Colors.purple,
              ),
            ),
          ],
        ));
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  title,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              // Search Field
              Expanded(
                flex: 2,
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText:
                        'Search by user name, ID, email, or description...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                  ),
                  onChanged: _activityController.setSearchQuery,
                ),
              ),
              const SizedBox(width: 16),

              // Activity Type Filter
              Expanded(
                child: Obx(() => DropdownButtonFormField<UserActivityType?>(
                      value: _activityController.selectedActivityType,
                      decoration: InputDecoration(
                        labelText: 'Filter by Type',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                      ),
                      items: [
                        const DropdownMenuItem<UserActivityType?>(
                          value: null,
                          child: Text('All Types'),
                        ),
                        ...UserActivityType.values
                            .map((type) => DropdownMenuItem(
                                  value: type,
                                  child: Text(type.name.toUpperCase()),
                                )),
                      ],
                      onChanged: _activityController.setActivityTypeFilter,
                    )),
              ),
              const SizedBox(width: 16),

              // Date Range Filter
              Expanded(
                child: Obx(() => OutlinedButton.icon(
                      onPressed: _showDateRangePicker,
                      icon: const Icon(Icons.date_range),
                      label: Text(
                        _activityController.selectedDateRange != null
                            ? '${_formatDate(_activityController.selectedDateRange!.start)} - ${_formatDate(_activityController.selectedDateRange!.end)}'
                            : 'Date Range',
                      ),
                    )),
              ),
              const SizedBox(width: 16),

              // Clear Filters Button
              ElevatedButton.icon(
                onPressed: () {
                  _searchController.clear();
                  _activityController.clearFilters();
                },
                icon: const Icon(Icons.clear),
                label: const Text('Clear'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[100],
                  foregroundColor: Colors.grey[700],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActivityList() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // List Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Expanded(
                    flex: 2,
                    child: Text('Activity',
                        style: TextStyle(fontWeight: FontWeight.bold))),
                const Expanded(
                    child: Text('User',
                        style: TextStyle(fontWeight: FontWeight.bold))),
                const Expanded(
                    child: Text('Type',
                        style: TextStyle(fontWeight: FontWeight.bold))),
                const Expanded(
                    child: Text('Timestamp',
                        style: TextStyle(fontWeight: FontWeight.bold))),
                const Expanded(
                    child: Text('IP Address',
                        style: TextStyle(fontWeight: FontWeight.bold))),
              ],
            ),
          ),

          // Activity List
          Expanded(
            child: Obx(() {
              if (_activityController.isLoading) {
                return const Center(child: CircularProgressIndicator());
              }

              if (_activityController.activities.isEmpty) {
                return const Center(
                  child: Text('No activities found'),
                );
              }

              return ListView.builder(
                itemCount: _activityController.activities.length,
                itemBuilder: (context, index) {
                  final activity = _activityController.activities[index];
                  return _buildActivityRow(activity);
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityRow(UserActivityModel activity) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          // Activity Description
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity.description,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                if (activity.metadata != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    'Additional info available',
                    style: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 12,
                    ),
                  ),
                ],
              ],
            ),
          ),

          // User Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _activityController.getUserDisplayName(activity.userId),
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
                Text(
                  activity.userId,
                  style: TextStyle(
                    fontFamily: 'monospace',
                    fontSize: 10,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),

          // Activity Type
          Expanded(
            child: ActivityTypeChip(type: activity.type),
          ),

          // Timestamp
          Expanded(
            child: Text(
              _formatDateTime(activity.timestamp),
              style: const TextStyle(fontSize: 12),
            ),
          ),

          // IP Address
          Expanded(
            child: Text(
              activity.ipAddress ?? 'N/A',
              style: const TextStyle(
                fontFamily: 'monospace',
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showDateRangePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: _activityController.selectedDateRange,
    );

    if (picked != null) {
      _activityController.setDateRangeFilter(picked);
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _calculatePeakHour() {
    if (_activityController.activities.isEmpty) return 'N/A';

    final hourCounts = <int, int>{};
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);

    // Count activities by hour for today
    for (final activity in _activityController.activities) {
      if (activity.timestamp.isAfter(startOfDay)) {
        final hour = activity.timestamp.hour;
        hourCounts[hour] = (hourCounts[hour] ?? 0) + 1;
      }
    }

    if (hourCounts.isEmpty) return 'N/A';

    // Find the hour with most activities
    final peakHour =
        hourCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key;
    return '${peakHour.toString().padLeft(2, '0')}:00';
  }
}
