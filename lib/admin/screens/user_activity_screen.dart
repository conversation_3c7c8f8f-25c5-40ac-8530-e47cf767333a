import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/user_activity_controller.dart';
import '../models/user_management_model.dart';
import '../theme/admin_theme.dart';
import '../widgets/mobile_admin_layout.dart';
import '../widgets/activity_type_chip.dart';

class UserActivityScreen extends StatefulWidget {
  const UserActivityScreen({super.key});

  @override
  State<UserActivityScreen> createState() => _UserActivityScreenState();
}

class _UserActivityScreenState extends State<UserActivityScreen> {
  final _activityController = Get.put(UserActivityController());
  final _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MobileAdminLayout(
      title: 'User Activity',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Head<PERSON> and Stats
          _buildHeaderSection(),
          const SizedBox(height: AdminTheme.spacingLarge),

          // Filters and Search
          _buildFiltersSection(),
          const SizedBox(height: AdminTheme.spacingLarge),

          // Activity List
          Expanded(child: _buildActivityList()),
        ],
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Obx(() {
      final screenWidth = MediaQuery.of(context).size.width;
      final isMobile = screenWidth < 768;

      if (isMobile) {
        return Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Activities',
                    _activityController.totalActivities.toString(),
                    Icons.timeline,
                    AdminTheme.primaryColor,
                  ),
                ),
                const SizedBox(width: AdminTheme.spacingMedium),
                Expanded(
                  child: _buildStatCard(
                    'Today\'s Activities',
                    _activityController.todayActivities.toString(),
                    Icons.today,
                    AdminTheme.successColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AdminTheme.spacingMedium),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Online Users',
                    _activityController.onlineUsers.toString(),
                    Icons.people,
                    AdminTheme.warningColor,
                  ),
                ),
                const SizedBox(width: AdminTheme.spacingMedium),
                Expanded(
                  child: _buildStatCard(
                    'Peak Hour',
                    _calculatePeakHour(),
                    Icons.schedule,
                    AdminTheme.errorColor,
                  ),
                ),
              ],
            ),
          ],
        );
      } else {
        return Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Total Activities',
                _activityController.totalActivities.toString(),
                Icons.timeline,
                AdminTheme.primaryColor,
              ),
            ),
            const SizedBox(width: AdminTheme.spacingMedium),
            Expanded(
              child: _buildStatCard(
                'Today\'s Activities',
                _activityController.todayActivities.toString(),
                Icons.today,
                AdminTheme.successColor,
              ),
            ),
            const SizedBox(width: AdminTheme.spacingMedium),
            Expanded(
              child: _buildStatCard(
                'Online Users',
                _activityController.onlineUsers.toString(),
                Icons.people,
                AdminTheme.warningColor,
              ),
            ),
            const SizedBox(width: AdminTheme.spacingMedium),
            Expanded(
              child: _buildStatCard(
                'Peak Hour',
                _calculatePeakHour(),
                Icons.schedule,
                AdminTheme.errorColor,
              ),
            ),
          ],
        );
      }
    });
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;

    return Container(
      padding: EdgeInsets.all(
          isMobile ? AdminTheme.spacingMedium : AdminTheme.spacingLarge),
      decoration: BoxDecoration(
        color: AdminTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
        boxShadow: AdminTheme.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(isMobile ? 8 : 12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AdminTheme.radiusSmall),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: isMobile ? 20 : 24,
                ),
              ),
              const Spacer(),
              if (!isMobile)
                Icon(
                  Icons.trending_up,
                  color: AdminTheme.textSecondary,
                  size: 16,
                ),
            ],
          ),
          SizedBox(
              height: isMobile
                  ? AdminTheme.spacingSmall
                  : AdminTheme.spacingMedium),
          Text(
            value,
            style: AdminTheme.headingMedium.copyWith(
              fontSize: isMobile ? 20 : 24,
              fontWeight: FontWeight.w700,
              color: AdminTheme.textPrimary,
            ),
          ),
          const SizedBox(height: AdminTheme.spacingXSmall),
          Text(
            title,
            style: AdminTheme.bodySmall.copyWith(
              color: AdminTheme.textSecondary,
              fontSize: isMobile ? 12 : 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              // Search Field
              Expanded(
                flex: 2,
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText:
                        'Search by user name, ID, email, or description...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                  ),
                  onChanged: _activityController.setSearchQuery,
                ),
              ),
              const SizedBox(width: 16),

              // Activity Type Filter
              Expanded(
                child: Obx(() => DropdownButtonFormField<UserActivityType?>(
                      value: _activityController.selectedActivityType,
                      decoration: InputDecoration(
                        labelText: 'Filter by Type',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                      ),
                      items: [
                        const DropdownMenuItem<UserActivityType?>(
                          value: null,
                          child: Text('All Types'),
                        ),
                        ...UserActivityType.values
                            .map((type) => DropdownMenuItem(
                                  value: type,
                                  child: Text(type.name.toUpperCase()),
                                )),
                      ],
                      onChanged: _activityController.setActivityTypeFilter,
                    )),
              ),
              const SizedBox(width: 16),

              // Date Range Filter
              Expanded(
                child: Obx(() => OutlinedButton.icon(
                      onPressed: _showDateRangePicker,
                      icon: const Icon(Icons.date_range),
                      label: Text(
                        _activityController.selectedDateRange != null
                            ? '${_formatDate(_activityController.selectedDateRange!.start)} - ${_formatDate(_activityController.selectedDateRange!.end)}'
                            : 'Date Range',
                      ),
                    )),
              ),
              const SizedBox(width: 16),

              // Clear Filters Button
              ElevatedButton.icon(
                onPressed: () {
                  _searchController.clear();
                  _activityController.clearFilters();
                },
                icon: const Icon(Icons.clear),
                label: const Text('Clear'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[100],
                  foregroundColor: Colors.grey[700],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActivityList() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // List Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Expanded(
                    flex: 2,
                    child: Text('Activity',
                        style: TextStyle(fontWeight: FontWeight.bold))),
                const Expanded(
                    child: Text('User',
                        style: TextStyle(fontWeight: FontWeight.bold))),
                const Expanded(
                    child: Text('Type',
                        style: TextStyle(fontWeight: FontWeight.bold))),
                const Expanded(
                    child: Text('Timestamp',
                        style: TextStyle(fontWeight: FontWeight.bold))),
                const Expanded(
                    child: Text('IP Address',
                        style: TextStyle(fontWeight: FontWeight.bold))),
              ],
            ),
          ),

          // Activity List
          Expanded(
            child: Obx(() {
              if (_activityController.isLoading) {
                return const Center(child: CircularProgressIndicator());
              }

              if (_activityController.activities.isEmpty) {
                return const Center(
                  child: Text('No activities found'),
                );
              }

              return ListView.builder(
                itemCount: _activityController.activities.length,
                itemBuilder: (context, index) {
                  final activity = _activityController.activities[index];
                  return _buildActivityRow(activity);
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityRow(UserActivityModel activity) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          // Activity Description
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity.description,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                if (activity.metadata != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    'Additional info available',
                    style: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 12,
                    ),
                  ),
                ],
              ],
            ),
          ),

          // User Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _activityController.getUserDisplayName(activity.userId),
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
                Text(
                  activity.userId,
                  style: TextStyle(
                    fontFamily: 'monospace',
                    fontSize: 10,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),

          // Activity Type
          Expanded(
            child: ActivityTypeChip(type: activity.type),
          ),

          // Timestamp
          Expanded(
            child: Text(
              _formatDateTime(activity.timestamp),
              style: const TextStyle(fontSize: 12),
            ),
          ),

          // IP Address
          Expanded(
            child: Text(
              activity.ipAddress ?? 'N/A',
              style: const TextStyle(
                fontFamily: 'monospace',
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showDateRangePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: _activityController.selectedDateRange,
    );

    if (picked != null) {
      _activityController.setDateRangeFilter(picked);
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _calculatePeakHour() {
    if (_activityController.activities.isEmpty) return 'N/A';

    final hourCounts = <int, int>{};
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);

    // Count activities by hour for today
    for (final activity in _activityController.activities) {
      if (activity.timestamp.isAfter(startOfDay)) {
        final hour = activity.timestamp.hour;
        hourCounts[hour] = (hourCounts[hour] ?? 0) + 1;
      }
    }

    if (hourCounts.isEmpty) return 'N/A';

    // Find the hour with most activities
    final peakHour =
        hourCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key;
    return '${peakHour.toString().padLeft(2, '0')}:00';
  }
}
