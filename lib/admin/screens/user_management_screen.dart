import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/user_management_controller.dart';
import '../models/user_management_model.dart';
import '../theme/admin_theme.dart';
import '../widgets/mobile_admin_layout.dart';
import '../widgets/admin_bottom_sheet.dart';
import '../widgets/user_details_dialog.dart';
import '../widgets/user_status_chip.dart';

class UserManagementScreen extends StatefulWidget {
  const UserManagementScreen({super.key});

  @override
  State<UserManagementScreen> createState() => _UserManagementScreenState();
}

class _UserManagementScreenState extends State<UserManagementScreen> {
  final _userManagementController = Get.put(UserManagementController());
  final _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MobileAdminLayout(
      title: 'User Management',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header and Stats
          _buildHeaderSection(),
          const SizedBox(height: AdminTheme.spacingLarge),

          // Filters and Search
          _buildFiltersSection(),
          const SizedBox(height: AdminTheme.spacingLarge),

          // Users Table
          Expanded(child: _buildUsersTable()),
        ],
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Obx(() {
      final screenWidth = MediaQuery.of(context).size.width;
      final isMobile = screenWidth < 768;

      if (isMobile) {
        return Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Users',
                    _userManagementController.totalUsers.toString(),
                    Icons.people,
                    AdminTheme.primaryColor,
                  ),
                ),
                const SizedBox(width: AdminTheme.spacingMedium),
                Expanded(
                  child: _buildStatCard(
                    'Active Users',
                    _userManagementController.activeUsers.toString(),
                    Icons.person,
                    AdminTheme.successColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AdminTheme.spacingSmall),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Blocked Users',
                    _userManagementController.blockedUsers.toString(),
                    Icons.block,
                    AdminTheme.errorColor,
                  ),
                ),
                const SizedBox(width: AdminTheme.spacingMedium),
                Expanded(
                  child: _buildStatCard(
                    'New Today',
                    _calculateNewUsersToday().toString(),
                    Icons.person_add,
                    AdminTheme.warningColor,
                  ),
                ),
              ],
            ),
          ],
        );
      } else {
        return Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Total Users',
                _userManagementController.totalUsers.toString(),
                Icons.people,
                AdminTheme.primaryColor,
              ),
            ),
            const SizedBox(width: AdminTheme.spacingMedium),
            Expanded(
              child: _buildStatCard(
                'Active Users',
                _userManagementController.activeUsers.toString(),
                Icons.person,
                AdminTheme.successColor,
              ),
            ),
            const SizedBox(width: AdminTheme.spacingMedium),
            Expanded(
              child: _buildStatCard(
                'Blocked Users',
                _userManagementController.blockedUsers.toString(),
                Icons.block,
                AdminTheme.errorColor,
              ),
            ),
            const SizedBox(width: AdminTheme.spacingMedium),
            Expanded(
              child: _buildStatCard(
                'New Today',
                _calculateNewUsersToday().toString(),
                Icons.person_add,
                AdminTheme.warningColor,
              ),
            ),
          ],
        );
      }
    });
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;

    return Container(
      padding: EdgeInsets.all(
          isMobile ? AdminTheme.spacingMedium : AdminTheme.spacingLarge),
      decoration: BoxDecoration(
        color: AdminTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
        boxShadow: AdminTheme.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(isMobile ? 8 : 12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AdminTheme.radiusSmall),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: isMobile ? 20 : 24,
                ),
              ),
              const Spacer(),
              if (!isMobile)
                Icon(
                  Icons.trending_up,
                  color: AdminTheme.textSecondary,
                  size: 16,
                ),
            ],
          ),
          SizedBox(
              height: isMobile
                  ? AdminTheme.spacingSmall
                  : AdminTheme.spacingMedium),
          Text(
            value,
            style: AdminTheme.headingMedium.copyWith(
              fontSize: isMobile ? 20 : 24,
              fontWeight: FontWeight.w700,
              color: AdminTheme.textPrimary,
            ),
          ),
          const SizedBox(height: AdminTheme.spacingXSmall),
          Text(
            title,
            style: AdminTheme.bodySmall.copyWith(
              color: AdminTheme.textSecondary,
              fontSize: isMobile ? 12 : 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;

    return Container(
      padding: EdgeInsets.all(
          isMobile ? AdminTheme.spacingMedium : AdminTheme.spacingLarge),
      decoration: BoxDecoration(
        color: AdminTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
        boxShadow: AdminTheme.cardShadow,
      ),
      child: isMobile ? _buildMobileFilters() : _buildDesktopFilters(),
    );
  }

  Widget _buildMobileFilters() {
    return Column(
      children: [
        // Search Field
        TextField(
          controller: _searchController,
          decoration: AdminTheme.inputDecoration(
            labelText: 'Search users...',
            hintText: 'Email, name, or username',
            prefixIcon: const Icon(Icons.search),
          ),
          onChanged: _userManagementController.setSearchQuery,
        ),
        const SizedBox(height: AdminTheme.spacingSmall),

        // Status Filter and Clear Button Row
        Row(
          children: [
            Expanded(
              flex: 2,
              child: Obx(() => DropdownButtonFormField<UserStatus?>(
                    value: _userManagementController.selectedStatus,
                    decoration: AdminTheme.inputDecoration(
                      labelText: 'Status',
                      prefixIcon: const Icon(Icons.filter_list),
                    ),
                    items: [
                      const DropdownMenuItem<UserStatus?>(
                        value: null,
                        child: Text('All'),
                      ),
                      ...UserStatus.values.map((status) => DropdownMenuItem(
                            value: status,
                            child: Text(status.name.toUpperCase()),
                          )),
                    ],
                    onChanged: _userManagementController.setStatusFilter,
                  )),
            ),
            const SizedBox(width: AdminTheme.spacingMedium),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  _searchController.clear();
                  _userManagementController.clearFilters();
                },
                icon: const Icon(Icons.clear, size: 18),
                label: const Text('Clear'),
                style: AdminTheme.secondaryButtonStyle,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDesktopFilters() {
    return Row(
      children: [
        // Search Field
        Expanded(
          flex: 2,
          child: TextField(
            controller: _searchController,
            decoration: AdminTheme.inputDecoration(
              labelText: 'Search users...',
              hintText: 'Search by email, name, or username',
              prefixIcon: const Icon(Icons.search),
            ),
            onChanged: _userManagementController.setSearchQuery,
          ),
        ),
        const SizedBox(width: AdminTheme.spacingMedium),

        // Status Filter
        Expanded(
          child: Obx(() => DropdownButtonFormField<UserStatus?>(
                value: _userManagementController.selectedStatus,
                decoration: AdminTheme.inputDecoration(
                  labelText: 'Filter by Status',
                  prefixIcon: const Icon(Icons.filter_list),
                ),
                items: [
                  const DropdownMenuItem<UserStatus?>(
                    value: null,
                    child: Text('All Statuses'),
                  ),
                  ...UserStatus.values.map((status) => DropdownMenuItem(
                        value: status,
                        child: Text(status.name.toUpperCase()),
                      )),
                ],
                onChanged: _userManagementController.setStatusFilter,
              )),
        ),
        const SizedBox(width: AdminTheme.spacingMedium),

        // Clear Filters Button
        ElevatedButton.icon(
          onPressed: () {
            _searchController.clear();
            _userManagementController.clearFilters();
          },
          icon: const Icon(Icons.clear),
          label: const Text('Clear'),
          style: AdminTheme.secondaryButtonStyle,
        ),
      ],
    );
  }

  Widget _buildUsersTable() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;

    return Container(
      decoration: BoxDecoration(
        color: AdminTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
        boxShadow: AdminTheme.cardShadow,
      ),
      child: Column(
        children: [
          // Table Header (only for desktop)
          if (!isMobile)
            Container(
              padding: const EdgeInsets.all(AdminTheme.spacingMedium),
              decoration: BoxDecoration(
                color: AdminTheme.backgroundColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AdminTheme.radiusMedium),
                  topRight: Radius.circular(AdminTheme.radiusMedium),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      'User',
                      style: AdminTheme.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AdminTheme.textPrimary,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      'Status',
                      style: AdminTheme.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AdminTheme.textPrimary,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      'Joined',
                      style: AdminTheme.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AdminTheme.textPrimary,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      'Last Login',
                      style: AdminTheme.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AdminTheme.textPrimary,
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 100,
                    child: Text(
                      'Actions',
                      style: AdminTheme.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AdminTheme.textPrimary,
                      ),
                    ),
                  ),
                ],
              ),
            ),

          // Table Body
          Expanded(
            child: Obx(() {
              if (_userManagementController.isLoading) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(
                        color: AdminTheme.primaryColor,
                      ),
                      const SizedBox(height: AdminTheme.spacingMedium),
                      Text(
                        'Loading users...',
                        style: AdminTheme.bodyMedium.copyWith(
                          color: AdminTheme.textSecondary,
                        ),
                      ),
                    ],
                  ),
                );
              }

              if (_userManagementController.users.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.people_outline,
                        size: 64,
                        color: AdminTheme.textSecondary,
                      ),
                      const SizedBox(height: AdminTheme.spacingMedium),
                      Text(
                        'No users found',
                        style: AdminTheme.headingSmall.copyWith(
                          color: AdminTheme.textSecondary,
                        ),
                      ),
                      const SizedBox(height: AdminTheme.spacingSmall),
                      Text(
                        'Try adjusting your search or filters',
                        style: AdminTheme.bodyMedium.copyWith(
                          color: AdminTheme.textSecondary,
                        ),
                      ),
                    ],
                  ),
                );
              }

              return ListView.builder(
                padding: EdgeInsets.all(isMobile ? AdminTheme.spacingSmall : 0),
                itemCount: _userManagementController.users.length,
                itemBuilder: (context, index) {
                  final user = _userManagementController.users[index];
                  return isMobile
                      ? _buildMobileUserCard(user)
                      : _buildDesktopUserRow(user);
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileUserCard(UserManagementModel user) {
    return Container(
      margin: const EdgeInsets.only(bottom: AdminTheme.spacingMedium),
      padding: const EdgeInsets.all(AdminTheme.spacingMedium),
      decoration: BoxDecoration(
        color: AdminTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
        boxShadow: AdminTheme.cardShadow,
        border: Border.all(color: AdminTheme.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User Header
          Row(
            children: [
              CircleAvatar(
                backgroundColor: AdminTheme.primaryColor,
                radius: 24,
                backgroundImage: user.profileImageUrl != null
                    ? NetworkImage(user.profileImageUrl!)
                    : null,
                child: user.profileImageUrl == null
                    ? Text(
                        user.name?.substring(0, 1).toUpperCase() ?? 'U',
                        style: AdminTheme.bodyLarge.copyWith(
                          color: AdminTheme.textOnPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      )
                    : null,
              ),
              const SizedBox(width: AdminTheme.spacingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.name ?? 'No Name',
                      style: AdminTheme.bodyLarge.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AdminTheme.textPrimary,
                      ),
                    ),
                    Text(
                      user.email,
                      style: AdminTheme.bodySmall.copyWith(
                        color: AdminTheme.textSecondary,
                      ),
                    ),
                    if (user.username != null)
                      Text(
                        '@${user.username}',
                        style: AdminTheme.bodySmall.copyWith(
                          color: AdminTheme.primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                  ],
                ),
              ),
              UserStatusChip(status: user.status),
            ],
          ),

          const SizedBox(height: AdminTheme.spacingSmall),

          // User Details
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'Joined',
                  _formatDate(user.createdAt),
                  Icons.calendar_today,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'Last Login',
                  user.lastLogin != null
                      ? _formatDate(user.lastLogin!)
                      : 'Never',
                  Icons.access_time,
                ),
              ),
            ],
          ),

          const SizedBox(height: AdminTheme.spacingSmall),

          // Actions
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _showUserDetails(user),
                  icon: const Icon(Icons.visibility, size: 16),
                  label: const Text('View'),
                  style: AdminTheme.secondaryButtonStyle,
                ),
              ),
              const SizedBox(width: AdminTheme.spacingSmall),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _handleUserAction('edit', user),
                  icon: const Icon(Icons.edit, size: 16),
                  label: const Text('Edit'),
                  style: AdminTheme.secondaryButtonStyle,
                ),
              ),
              const SizedBox(width: AdminTheme.spacingSmall),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _handleUserAction(
                    user.isBlocked ? 'unblock' : 'block',
                    user,
                  ),
                  icon: Icon(
                    user.isBlocked ? Icons.check_circle : Icons.block,
                    size: 16,
                  ),
                  label: Text(user.isBlocked ? 'Unblock' : 'Block'),
                  style: user.isBlocked
                      ? AdminTheme.successButtonStyle
                      : AdminTheme.errorButtonStyle,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: AdminTheme.textSecondary,
        ),
        const SizedBox(width: AdminTheme.spacingSmall),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AdminTheme.bodySmall.copyWith(
                  color: AdminTheme.textSecondary,
                  fontSize: 11,
                ),
              ),
              Text(
                value,
                style: AdminTheme.bodySmall.copyWith(
                  color: AdminTheme.textPrimary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDesktopUserRow(UserManagementModel user) {
    return Container(
      padding: const EdgeInsets.all(AdminTheme.spacingMedium),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AdminTheme.borderColor),
        ),
      ),
      child: Row(
        children: [
          // User Info
          Expanded(
            flex: 2,
            child: Row(
              children: [
                CircleAvatar(
                  backgroundColor: AdminTheme.primaryColor,
                  radius: 20,
                  backgroundImage: user.profileImageUrl != null
                      ? NetworkImage(user.profileImageUrl!)
                      : null,
                  child: user.profileImageUrl == null
                      ? Text(
                          user.name?.substring(0, 1).toUpperCase() ?? 'U',
                          style: AdminTheme.bodyMedium.copyWith(
                            color: AdminTheme.textOnPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        )
                      : null,
                ),
                const SizedBox(width: AdminTheme.spacingMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user.name ?? 'No Name',
                        style: AdminTheme.bodyMedium.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AdminTheme.textPrimary,
                        ),
                      ),
                      Text(
                        user.email,
                        style: AdminTheme.bodySmall.copyWith(
                          color: AdminTheme.textSecondary,
                        ),
                      ),
                      if (user.username != null)
                        Text(
                          '@${user.username}',
                          style: AdminTheme.bodySmall.copyWith(
                            color: AdminTheme.primaryColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Status
          Expanded(
            child: UserStatusChip(status: user.status),
          ),

          // Joined Date
          Expanded(
            child: Text(
              _formatDate(user.createdAt),
              style: AdminTheme.bodySmall.copyWith(
                color: AdminTheme.textPrimary,
              ),
            ),
          ),

          // Last Login
          Expanded(
            child: Text(
              user.lastLogin != null ? _formatDate(user.lastLogin!) : 'Never',
              style: AdminTheme.bodySmall.copyWith(
                color: AdminTheme.textPrimary,
              ),
            ),
          ),

          // Actions
          SizedBox(
            width: 100,
            child: Row(
              children: [
                IconButton(
                  icon: Icon(
                    Icons.visibility,
                    size: 18,
                    color: AdminTheme.primaryColor,
                  ),
                  onPressed: () => _showUserDetails(user),
                  tooltip: 'View Details',
                ),
                PopupMenuButton<String>(
                  icon: Icon(
                    Icons.more_vert,
                    size: 18,
                    color: AdminTheme.textSecondary,
                  ),
                  onSelected: (value) => _handleUserAction(value, user),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: ListTile(
                        leading: Icon(Icons.edit, size: 16),
                        title: Text('Edit'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    PopupMenuItem(
                      value: user.isBlocked ? 'unblock' : 'block',
                      child: ListTile(
                        leading: Icon(
                          user.isBlocked ? Icons.check_circle : Icons.block,
                          size: 16,
                          color: user.isBlocked ? Colors.green : Colors.red,
                        ),
                        title: Text(user.isBlocked ? 'Unblock' : 'Block'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  int _calculateNewUsersToday() {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);

    return _userManagementController.users.where((user) {
      return user.createdAt.isAfter(startOfDay);
    }).length;
  }

  void _showUserDetails(UserManagementModel user) {
    AdminBottomSheet.show(
      context: context,
      title: 'User Details: ${user.name}',
      child: UserDetailsDialog(user: user),
      height: MediaQuery.of(context).size.height * 0.8,
    );
  }

  void _handleUserAction(String action, UserManagementModel user) {
    switch (action) {
      case 'edit':
        _showEditUserDialog(user);
        break;
      case 'block':
        _showBlockUserDialog(user);
        break;
      case 'unblock':
        _unblockUser(user);
        break;
    }
  }

  void _showBlockUserDialog(UserManagementModel user) {
    final reasonController = TextEditingController();

    FormBottomSheet.show(
      context: context,
      title: 'Block User: ${user.name}',
      fields: [
        Container(
          padding: const EdgeInsets.all(AdminTheme.spacingMedium),
          decoration: BoxDecoration(
            color: AdminTheme.errorColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
            border: Border.all(color: AdminTheme.errorColor.withOpacity(0.3)),
          ),
          child: Row(
            children: [
              Icon(Icons.warning, color: AdminTheme.errorColor),
              const SizedBox(width: AdminTheme.spacingMedium),
              Expanded(
                child: Text(
                  'This action will block the user from accessing the app.',
                  style: AdminTheme.bodyMedium.copyWith(
                    color: AdminTheme.errorColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
        TextField(
          controller: reasonController,
          decoration: AdminTheme.inputDecoration(
            labelText: 'Reason for blocking',
            prefixIcon: const Icon(Icons.block, color: AdminTheme.errorColor),
            hintText: 'Enter the reason for blocking this user...',
          ),
          maxLines: 3,
        ),
      ],
      onSave: () async {
        if (reasonController.text.trim().isNotEmpty) {
          Navigator.of(context).pop();
          final success = await _userManagementController.blockUser(
            user.id,
            reasonController.text.trim(),
          );
          if (success) {
            Get.snackbar(
              'Success',
              'User blocked successfully',
              backgroundColor: AdminTheme.successColor,
              colorText: AdminTheme.textOnPrimary,
            );
          }
        } else {
          Get.snackbar(
            'Error',
            'Please provide a reason for blocking',
            backgroundColor: AdminTheme.errorColor,
            colorText: AdminTheme.textOnPrimary,
          );
        }
      },
      saveText: 'Block User',
    );
  }

  void _unblockUser(UserManagementModel user) {
    ConfirmationBottomSheet.show(
      context: context,
      title: 'Unblock User',
      message:
          'Are you sure you want to unblock ${user.name}? This will restore their access to the app.',
      confirmText: 'Unblock User',
      cancelText: 'Cancel',
      confirmColor: AdminTheme.successColor,
      icon: Icons.check_circle,
      onConfirm: () async {
        final success = await _userManagementController.unblockUser(user.id);
        if (success) {
          Get.snackbar(
            'Success',
            'User unblocked successfully',
            backgroundColor: AdminTheme.successColor,
            colorText: AdminTheme.textOnPrimary,
          );
        }
      },
    );
  }

  void _showEditUserDialog(UserManagementModel user) {
    final nameController = TextEditingController(text: user.name);
    final usernameController = TextEditingController(text: user.username);
    final bioController = TextEditingController(text: user.bio);

    FormBottomSheet.show(
      context: context,
      title: 'Edit User: ${user.email}',
      fields: [
        TextField(
          controller: nameController,
          decoration: AdminTheme.inputDecoration(
            labelText: 'Full Name',
            prefixIcon: const Icon(Icons.person),
          ),
        ),
        TextField(
          controller: usernameController,
          decoration: AdminTheme.inputDecoration(
            labelText: 'Username',
            prefixIcon: const Icon(Icons.alternate_email),
          ),
        ),
        TextField(
          controller: bioController,
          decoration: AdminTheme.inputDecoration(
            labelText: 'Bio',
            prefixIcon: const Icon(Icons.info),
          ),
          maxLines: 3,
        ),
      ],
      onSave: () async {
        final updates = <String, dynamic>{};

        if (nameController.text.trim() != user.name) {
          updates['name'] = nameController.text.trim();
        }
        if (usernameController.text.trim() != user.username) {
          updates['username'] = usernameController.text.trim();
        }
        if (bioController.text.trim() != user.bio) {
          updates['bio'] = bioController.text.trim();
        }

        if (updates.isNotEmpty) {
          Navigator.of(context).pop();
          final success = await _userManagementController.updateUserProfile(
              user.id, updates);
          if (success) {
            Get.snackbar(
              'Success',
              'User profile updated successfully',
              backgroundColor: AdminTheme.successColor,
              colorText: AdminTheme.textOnPrimary,
            );
          }
        } else {
          Navigator.of(context).pop();
        }
      },
      saveText: 'Update User',
    );
  }
}
