import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/user_management_controller.dart';
import '../models/user_management_model.dart';
import '../widgets/admin_sidebar.dart';
import '../widgets/user_details_dialog.dart';
import '../widgets/user_status_chip.dart';

class UserManagementScreen extends StatefulWidget {
  const UserManagementScreen({super.key});

  @override
  State<UserManagementScreen> createState() => _UserManagementScreenState();
}

class _UserManagementScreenState extends State<UserManagementScreen> {
  final _userManagementController = Get.put(UserManagementController());
  final _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Row(
        children: [
          // Sidebar
          const AdminSidebar(),

          // Main Content
          Expanded(
            child: Column(
              children: [
                // Top App Bar
                _buildTopAppBar(),

                // Content
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header and Stats
                        _buildHeaderSection(),
                        const SizedBox(height: 24),

                        // Filters and Search
                        _buildFiltersSection(),
                        const SizedBox(height: 24),

                        // Users Table
                        Expanded(child: _buildUsersTable()),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopAppBar() {
    return Container(
      height: 70,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: Row(
          children: [
            Text(
              'User Management',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
            ),
            const Spacer(),

            // Refresh Button
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                // Refresh is automatic with streams
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Obx(() => Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Total Users',
                _userManagementController.totalUsers.toString(),
                Icons.people,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'Active Users',
                _userManagementController.activeUsers.toString(),
                Icons.person_pin_circle_outlined,
                Colors.green,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'Blocked Users',
                _userManagementController.blockedUsers.toString(),
                Icons.block,
                Colors.red,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'New Today',
                _calculateNewUsersToday().toString(),
                Icons.person_add,
                Colors.orange,
              ),
            ),
          ],
        ));
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  title,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Search Field
          Expanded(
            flex: 2,
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search users by email, name, or username...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              onChanged: _userManagementController.setSearchQuery,
            ),
          ),
          const SizedBox(width: 16),

          // Status Filter
          Expanded(
            child: Obx(() => DropdownButtonFormField<UserStatus?>(
                  value: _userManagementController.selectedStatus,
                  decoration: InputDecoration(
                    labelText: 'Filter by Status',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                  ),
                  items: [
                    const DropdownMenuItem<UserStatus?>(
                      value: null,
                      child: Text('All Statuses'),
                    ),
                    ...UserStatus.values.map((status) => DropdownMenuItem(
                          value: status,
                          child: Text(status.name.toUpperCase()),
                        )),
                  ],
                  onChanged: _userManagementController.setStatusFilter,
                )),
          ),
          const SizedBox(width: 16),

          // Clear Filters Button
          ElevatedButton.icon(
            onPressed: () {
              _searchController.clear();
              _userManagementController.clearFilters();
            },
            icon: const Icon(Icons.clear),
            label: const Text('Clear'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey[100],
              foregroundColor: Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsersTable() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Table Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Expanded(
                    flex: 2,
                    child: Text('User',
                        style: TextStyle(fontWeight: FontWeight.bold))),
                const Expanded(
                    child: Text('Status',
                        style: TextStyle(fontWeight: FontWeight.bold))),
                const Expanded(
                    child: Text('Joined',
                        style: TextStyle(fontWeight: FontWeight.bold))),
                const Expanded(
                    child: Text('Last Login',
                        style: TextStyle(fontWeight: FontWeight.bold))),
                const SizedBox(
                    width: 100,
                    child: Text('Actions',
                        style: TextStyle(fontWeight: FontWeight.bold))),
              ],
            ),
          ),

          // Table Body
          Expanded(
            child: Obx(() {
              if (_userManagementController.isLoading) {
                return const Center(child: CircularProgressIndicator());
              }

              if (_userManagementController.users.isEmpty) {
                return const Center(
                  child: Text('No users found'),
                );
              }

              return ListView.builder(
                itemCount: _userManagementController.users.length,
                itemBuilder: (context, index) {
                  final user = _userManagementController.users[index];
                  return _buildUserRow(user);
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildUserRow(UserManagementModel user) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          // User Info
          Expanded(
            flex: 2,
            child: Row(
              children: [
                CircleAvatar(
                  backgroundImage: user.profileImageUrl != null
                      ? NetworkImage(user.profileImageUrl!)
                      : null,
                  child: user.profileImageUrl == null
                      ? Text(user.name?.substring(0, 1).toUpperCase() ?? 'U')
                      : null,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user.name ?? 'No Name',
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
                      Text(
                        user.email,
                        style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      ),
                      if (user.username != null)
                        Text(
                          '@${user.username}',
                          style:
                              TextStyle(color: Colors.grey[500], fontSize: 12),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Status
          Expanded(
            child: UserStatusChip(status: user.status),
          ),

          // Joined Date
          Expanded(
            child: Text(
              _formatDate(user.createdAt),
              style: const TextStyle(fontSize: 12),
            ),
          ),

          // Last Login
          Expanded(
            child: Text(
              user.lastLogin != null ? _formatDate(user.lastLogin!) : 'Never',
              style: const TextStyle(fontSize: 12),
            ),
          ),

          // Actions
          SizedBox(
            width: 100,
            child: Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.visibility, size: 18),
                  onPressed: () => _showUserDetails(user),
                  tooltip: 'View Details',
                ),
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert, size: 18),
                  onSelected: (value) => _handleUserAction(value, user),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: ListTile(
                        leading: Icon(Icons.edit, size: 16),
                        title: Text('Edit'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    PopupMenuItem(
                      value: user.isBlocked ? 'unblock' : 'block',
                      child: ListTile(
                        leading: Icon(
                          user.isBlocked ? Icons.check_circle : Icons.block,
                          size: 16,
                          color: user.isBlocked ? Colors.green : Colors.red,
                        ),
                        title: Text(user.isBlocked ? 'Unblock' : 'Block'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  int _calculateNewUsersToday() {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);

    return _userManagementController.users.where((user) {
      return user.createdAt.isAfter(startOfDay);
    }).length;
  }

  void _showUserDetails(UserManagementModel user) {
    Get.dialog(
      UserDetailsDialog(user: user),
      barrierDismissible: true,
    );
  }

  void _handleUserAction(String action, UserManagementModel user) {
    switch (action) {
      case 'edit':
        _showEditUserDialog(user);
        break;
      case 'block':
        _showBlockUserDialog(user);
        break;
      case 'unblock':
        _unblockUser(user);
        break;
    }
  }

  void _showBlockUserDialog(UserManagementModel user) {
    final reasonController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: Text('Block User: ${user.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Are you sure you want to block this user?'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason for blocking',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (reasonController.text.trim().isNotEmpty) {
                Get.back();
                final success = await _userManagementController.blockUser(
                  user.id,
                  reasonController.text.trim(),
                );
                if (success) {
                  Get.snackbar('Success', 'User blocked successfully');
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Block User'),
          ),
        ],
      ),
    );
  }

  void _unblockUser(UserManagementModel user) {
    Get.dialog(
      AlertDialog(
        title: Text('Unblock User: ${user.name}'),
        content: const Text('Are you sure you want to unblock this user?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back();
              final success =
                  await _userManagementController.unblockUser(user.id);
              if (success) {
                Get.snackbar('Success', 'User unblocked successfully');
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
            child: const Text('Unblock User'),
          ),
        ],
      ),
    );
  }

  void _showEditUserDialog(UserManagementModel user) {
    final nameController = TextEditingController(text: user.name);
    final usernameController = TextEditingController(text: user.username);
    final bioController = TextEditingController(text: user.bio);

    Get.dialog(
      AlertDialog(
        title: Text('Edit User: ${user.email}'),
        content: SizedBox(
          width: 400,
          height: 400,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Full Name',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: usernameController,
                  decoration: const InputDecoration(
                    labelText: 'Username',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: bioController,
                  decoration: const InputDecoration(
                    labelText: 'Bio',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final updates = <String, dynamic>{};

              if (nameController.text.trim() != user.name) {
                updates['name'] = nameController.text.trim();
              }
              if (usernameController.text.trim() != user.username) {
                updates['username'] = usernameController.text.trim();
              }
              if (bioController.text.trim() != user.bio) {
                updates['bio'] = bioController.text.trim();
              }

              if (updates.isNotEmpty) {
                Get.back();
                final success = await _userManagementController
                    .updateUserProfile(user.id, updates);
                if (success) {
                  Get.snackbar('Success', 'User profile updated successfully');
                }
              } else {
                Get.back();
              }
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }
}
