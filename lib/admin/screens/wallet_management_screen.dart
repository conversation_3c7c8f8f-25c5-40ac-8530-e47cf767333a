import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/wallet_management_controller.dart';
import '../models/wallet_management_model.dart';
import '../theme/admin_theme.dart';
import '../widgets/mobile_admin_layout.dart';
import '../widgets/admin_bottom_sheet.dart';
import '../widgets/wallet_status_chip.dart';
import '../widgets/wallet_details_dialog.dart';

class WalletManagementScreen extends StatefulWidget {
  const WalletManagementScreen({super.key});

  @override
  State<WalletManagementScreen> createState() => _WalletManagementScreenState();
}

class _WalletManagementScreenState extends State<WalletManagementScreen> {
  final _walletController = Get.put(WalletManagementController());
  final _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MobileAdminLayout(
      title: 'Wallet Management',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header and Stats
          _buildHeaderSection(),
          const SizedBox(height: AdminTheme.spacingLarge),

          // Filters and Search
          _buildFiltersSection(),
          const SizedBox(height: AdminTheme.spacingLarge),

          // Wallets Table
          Expanded(child: _buildWalletsTable()),
        ],
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Obx(() {
      final screenWidth = MediaQuery.of(context).size.width;
      final isMobile = screenWidth < 768;

      if (isMobile) {
        return Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Wallets',
                    _walletController.totalWallets.toString(),
                    Icons.account_balance_wallet,
                    AdminTheme.primaryColor,
                  ),
                ),
                const SizedBox(width: AdminTheme.spacingMedium),
                Expanded(
                  child: _buildStatCard(
                    'Total Balance',
                    '\$${_walletController.totalBalance.toStringAsFixed(2)}',
                    Icons.attach_money,
                    AdminTheme.successColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AdminTheme.spacingMedium),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Active Wallets',
                    _walletController.totalWallets.toString(),
                    Icons.account_balance,
                    AdminTheme.warningColor,
                  ),
                ),
                const SizedBox(width: AdminTheme.spacingMedium),
                Expanded(
                  child: _buildStatCard(
                    'Transactions Today',
                    _calculateTransactionsToday().toString(),
                    Icons.swap_horiz,
                    AdminTheme.errorColor,
                  ),
                ),
              ],
            ),
          ],
        );
      } else {
        return Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Total Wallets',
                _walletController.totalWallets.toString(),
                Icons.account_balance_wallet,
                AdminTheme.primaryColor,
              ),
            ),
            const SizedBox(width: AdminTheme.spacingMedium),
            Expanded(
              child: _buildStatCard(
                'Total Balance',
                '\$${_walletController.totalBalance.toStringAsFixed(2)}',
                Icons.attach_money,
                AdminTheme.successColor,
              ),
            ),
            const SizedBox(width: AdminTheme.spacingMedium),
            Expanded(
              child: _buildStatCard(
                'Active Wallets',
                _walletController.totalWallets.toString(),
                Icons.account_balance,
                AdminTheme.warningColor,
              ),
            ),
            const SizedBox(width: AdminTheme.spacingMedium),
            Expanded(
              child: _buildStatCard(
                'Transactions Today',
                _calculateTransactionsToday().toString(),
                Icons.swap_horiz,
                AdminTheme.errorColor,
              ),
            ),
          ],
        );
      }
    });
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;

    return Container(
      padding: EdgeInsets.all(
          isMobile ? AdminTheme.spacingMedium : AdminTheme.spacingLarge),
      decoration: BoxDecoration(
        color: AdminTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
        boxShadow: AdminTheme.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(isMobile ? 8 : 12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AdminTheme.radiusSmall),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: isMobile ? 20 : 24,
                ),
              ),
              const Spacer(),
              if (!isMobile)
                Icon(
                  Icons.trending_up,
                  color: AdminTheme.textSecondary,
                  size: 16,
                ),
            ],
          ),
          SizedBox(
              height: isMobile
                  ? AdminTheme.spacingSmall
                  : AdminTheme.spacingMedium),
          Text(
            value,
            style: AdminTheme.headingMedium.copyWith(
              fontSize: isMobile ? 20 : 24,
              fontWeight: FontWeight.w700,
              color: AdminTheme.textPrimary,
            ),
          ),
          const SizedBox(height: AdminTheme.spacingXSmall),
          Text(
            title,
            style: AdminTheme.bodySmall.copyWith(
              color: AdminTheme.textSecondary,
              fontSize: isMobile ? 12 : 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Search Field
          Expanded(
            flex: 2,
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search by user ID, name, username, or email...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              onChanged: _walletController.setSearchQuery,
            ),
          ),
          const SizedBox(width: 16),

          // Status Filter
          Expanded(
            child: Obx(() => DropdownButtonFormField<WalletStatus?>(
                  value: _walletController.selectedStatus,
                  decoration: InputDecoration(
                    labelText: 'Filter by Status',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                  ),
                  items: [
                    const DropdownMenuItem<WalletStatus?>(
                      value: null,
                      child: Text('All Statuses'),
                    ),
                    ...WalletStatus.values.map((status) => DropdownMenuItem(
                          value: status,
                          child: Text(status.name.toUpperCase()),
                        )),
                  ],
                  onChanged: _walletController.setStatusFilter,
                )),
          ),
          const SizedBox(width: 16),

          // Clear Filters Button
          ElevatedButton.icon(
            onPressed: () {
              _searchController.clear();
              _walletController.clearFilters();
            },
            icon: const Icon(Icons.clear),
            label: const Text('Clear'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey[100],
              foregroundColor: Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWalletsTable() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Table Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Expanded(
                    flex: 2,
                    child: Text('User',
                        style: TextStyle(fontWeight: FontWeight.bold))),
                const Expanded(
                    child: Text('Balance',
                        style: TextStyle(fontWeight: FontWeight.bold))),
                const Expanded(
                    child: Text('Status',
                        style: TextStyle(fontWeight: FontWeight.bold))),
                const Expanded(
                    child: Text('Last Updated',
                        style: TextStyle(fontWeight: FontWeight.bold))),
                const Expanded(
                    child: Text('Transactions',
                        style: TextStyle(fontWeight: FontWeight.bold))),
                const SizedBox(
                    width: 100,
                    child: Text('Actions',
                        style: TextStyle(fontWeight: FontWeight.bold))),
              ],
            ),
          ),

          // Table Body
          Expanded(
            child: Obx(() {
              if (_walletController.isLoading) {
                return const Center(child: CircularProgressIndicator());
              }

              if (_walletController.wallets.isEmpty) {
                return const Center(
                  child: Text('No wallets found'),
                );
              }

              return ListView.builder(
                itemCount: _walletController.wallets.length,
                itemBuilder: (context, index) {
                  final wallet = _walletController.wallets[index];
                  return _buildWalletRow(wallet);
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildWalletRow(WalletManagementModel wallet) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          // User Info
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _walletController.getUserDisplayName(wallet.userId),
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                Text(
                  wallet.userId,
                  style: TextStyle(
                    fontFamily: 'monospace',
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),

          // Balance
          Expanded(
            child: Text(
              '\$${wallet.balance.toStringAsFixed(2)}',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: wallet.balance > 0 ? Colors.green : Colors.grey,
              ),
            ),
          ),

          // Status
          Expanded(
            child: WalletStatusChip(status: wallet.status),
          ),

          // Last Updated
          Expanded(
            child: Text(
              _formatDate(wallet.lastUpdated),
              style: const TextStyle(fontSize: 12),
            ),
          ),

          // Transaction Count
          Expanded(
            child: Text(
              wallet.transactionCount.toString(),
              style: const TextStyle(fontSize: 12),
            ),
          ),

          // Actions
          SizedBox(
            width: 100,
            child: Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.visibility, size: 18),
                  onPressed: () => _showWalletDetails(wallet),
                  tooltip: 'View Details',
                ),
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert, size: 18),
                  onSelected: (value) => _handleWalletAction(value, wallet),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'adjust',
                      child: ListTile(
                        leading: Icon(Icons.account_balance, size: 16),
                        title: Text('Adjust Balance'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'add_funds',
                      child: ListTile(
                        leading: Icon(Icons.add_circle,
                            size: 16, color: Colors.green),
                        title: Text('Add Funds'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'deduct_funds',
                      child: ListTile(
                        leading: Icon(Icons.remove_circle,
                            size: 16, color: Colors.red),
                        title: Text('Deduct Funds'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'transactions',
                      child: ListTile(
                        leading: Icon(Icons.list, size: 16),
                        title: Text('View Transactions'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showWalletDetails(WalletManagementModel wallet) {
    Get.dialog(
      WalletDetailsDialog(wallet: wallet),
      barrierDismissible: true,
    );
  }

  void _handleWalletAction(String action, WalletManagementModel wallet) {
    switch (action) {
      case 'adjust':
        _showAdjustBalanceDialog(wallet);
        break;
      case 'add_funds':
        _showAddFundsDialog(wallet);
        break;
      case 'deduct_funds':
        _showDeductFundsDialog(wallet);
        break;

      case 'transactions':
        _showTransactionsDialog(wallet);
        break;
    }
  }

  void _showAdjustBalanceDialog(WalletManagementModel wallet) {
    final amountController = TextEditingController();
    final reasonController = TextEditingController();

    FormBottomSheet.show(
      context: context,
      title:
          'Adjust Balance: ${_walletController.getUserDisplayName(wallet.userId)}',
      fields: [
        Container(
          padding: const EdgeInsets.all(AdminTheme.spacingMedium),
          decoration: BoxDecoration(
            color: AdminTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
            border: Border.all(color: AdminTheme.primaryColor.withOpacity(0.3)),
          ),
          child: Row(
            children: [
              Icon(Icons.account_balance_wallet,
                  color: AdminTheme.primaryColor),
              const SizedBox(width: AdminTheme.spacingMedium),
              Text(
                'Current Balance: \$${wallet.balance.toStringAsFixed(2)}',
                style: AdminTheme.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AdminTheme.primaryColor,
                ),
              ),
            ],
          ),
        ),
        TextField(
          controller: amountController,
          decoration: AdminTheme.inputDecoration(
            labelText: 'Adjustment Amount',
            prefixIcon: const Icon(Icons.edit, color: AdminTheme.primaryColor),
            hintText: '0.00',
            helperText: 'Use negative values to deduct (e.g., -50.00)',
          ),
          keyboardType: const TextInputType.numberWithOptions(
              decimal: true, signed: true),
        ),
        TextField(
          controller: reasonController,
          decoration: AdminTheme.inputDecoration(
            labelText: 'Reason for adjustment',
            prefixIcon: const Icon(Icons.note_add),
            hintText: 'Enter reason for this adjustment...',
          ),
          maxLines: 3,
        ),
      ],
      onSave: () async {
        final amount = double.tryParse(amountController.text);
        if (amount != null && reasonController.text.trim().isNotEmpty) {
          Navigator.of(context).pop();
          final success = await _walletController.adjustWalletBalance(
            wallet.userId,
            amount,
            reasonController.text.trim(),
          );
          if (success) {
            Get.snackbar(
              'Success',
              'Wallet balance adjusted successfully',
              backgroundColor: AdminTheme.successColor,
              colorText: AdminTheme.textOnPrimary,
            );
          }
        } else {
          Get.snackbar(
            'Error',
            'Please enter a valid amount and reason',
            backgroundColor: AdminTheme.errorColor,
            colorText: AdminTheme.textOnPrimary,
          );
        }
      },
      saveText: 'Adjust Balance',
    );
  }

  int _calculateTransactionsToday() {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);

    return _walletController.transactions.where((transaction) {
      return transaction.timestamp.isAfter(startOfDay);
    }).length;
  }

  void _showAddFundsDialog(WalletManagementModel wallet) {
    final amountController = TextEditingController();
    final reasonController = TextEditingController();

    FormBottomSheet.show(
      context: context,
      title:
          'Add Funds: ${_walletController.getUserDisplayName(wallet.userId)}',
      fields: [
        Container(
          padding: const EdgeInsets.all(AdminTheme.spacingMedium),
          decoration: BoxDecoration(
            color: AdminTheme.successColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
            border: Border.all(color: AdminTheme.successColor.withOpacity(0.3)),
          ),
          child: Row(
            children: [
              Icon(Icons.account_balance_wallet,
                  color: AdminTheme.successColor),
              const SizedBox(width: AdminTheme.spacingMedium),
              Text(
                'Current Balance: \$${wallet.balance.toStringAsFixed(2)}',
                style: AdminTheme.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AdminTheme.successColor,
                ),
              ),
            ],
          ),
        ),
        TextField(
          controller: amountController,
          decoration: AdminTheme.inputDecoration(
            labelText: 'Amount to Add',
            prefixIcon:
                const Icon(Icons.add_circle, color: AdminTheme.successColor),
            hintText: '0.00',
          ),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
        ),
        TextField(
          controller: reasonController,
          decoration: AdminTheme.inputDecoration(
            labelText: 'Reason for adding funds',
            prefixIcon: const Icon(Icons.note_add),
            hintText: 'Enter reason for this transaction...',
          ),
          maxLines: 3,
        ),
      ],
      onSave: () async {
        final amount = double.tryParse(amountController.text);
        if (amount != null &&
            amount > 0 &&
            reasonController.text.trim().isNotEmpty) {
          Navigator.of(context).pop();
          final success = await _walletController.adjustWalletBalance(
            wallet.userId,
            amount,
            reasonController.text.trim(),
          );
          if (success) {
            Get.snackbar(
              'Success',
              'Funds added successfully',
              backgroundColor: AdminTheme.successColor,
              colorText: AdminTheme.textOnPrimary,
            );
          }
        } else {
          Get.snackbar(
            'Error',
            'Please enter a valid amount and reason',
            backgroundColor: AdminTheme.errorColor,
            colorText: AdminTheme.textOnPrimary,
          );
        }
      },
      saveText: 'Add Funds',
    );
  }

  void _showDeductFundsDialog(WalletManagementModel wallet) {
    final amountController = TextEditingController();
    final reasonController = TextEditingController();

    FormBottomSheet.show(
      context: context,
      title:
          'Deduct Funds: ${_walletController.getUserDisplayName(wallet.userId)}',
      fields: [
        Container(
          padding: const EdgeInsets.all(AdminTheme.spacingMedium),
          decoration: BoxDecoration(
            color: AdminTheme.errorColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
            border: Border.all(color: AdminTheme.errorColor.withOpacity(0.3)),
          ),
          child: Row(
            children: [
              Icon(Icons.account_balance_wallet, color: AdminTheme.errorColor),
              const SizedBox(width: AdminTheme.spacingMedium),
              Text(
                'Current Balance: \$${wallet.balance.toStringAsFixed(2)}',
                style: AdminTheme.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AdminTheme.errorColor,
                ),
              ),
            ],
          ),
        ),
        TextField(
          controller: amountController,
          decoration: AdminTheme.inputDecoration(
            labelText: 'Amount to Deduct',
            prefixIcon:
                const Icon(Icons.remove_circle, color: AdminTheme.errorColor),
            hintText: '0.00',
            helperText: 'Maximum: \$${wallet.balance.toStringAsFixed(2)}',
          ),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
        ),
        TextField(
          controller: reasonController,
          decoration: AdminTheme.inputDecoration(
            labelText: 'Reason for deducting funds',
            prefixIcon: const Icon(Icons.note_add),
            hintText: 'Enter reason for this transaction...',
          ),
          maxLines: 3,
        ),
      ],
      onSave: () async {
        final amount = double.tryParse(amountController.text);
        if (amount != null &&
            amount > 0 &&
            reasonController.text.trim().isNotEmpty) {
          if (amount > wallet.balance) {
            Get.snackbar(
              'Error',
              'Cannot deduct more than current balance',
              backgroundColor: AdminTheme.errorColor,
              colorText: AdminTheme.textOnPrimary,
            );
            return;
          }
          Navigator.of(context).pop();
          final success = await _walletController.adjustWalletBalance(
            wallet.userId,
            -amount, // Negative amount for deduction
            reasonController.text.trim(),
          );
          if (success) {
            Get.snackbar(
              'Success',
              'Funds deducted successfully',
              backgroundColor: AdminTheme.successColor,
              colorText: AdminTheme.textOnPrimary,
            );
          }
        } else {
          Get.snackbar(
            'Error',
            'Please enter a valid amount and reason',
            backgroundColor: AdminTheme.errorColor,
            colorText: AdminTheme.textOnPrimary,
          );
        }
      },
      saveText: 'Deduct Funds',
    );
  }

  void _showTransactionsDialog(WalletManagementModel wallet) {
    Get.snackbar('Info',
        'Detailed transaction history is available in the wallet details dialog');
  }
}
