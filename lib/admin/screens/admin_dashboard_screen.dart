import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/admin_auth_controller.dart';
import '../controllers/user_management_controller.dart';
import '../controllers/wallet_management_controller.dart';
import '../theme/admin_theme.dart';
import '../widgets/admin_sidebar.dart';
import '../widgets/dashboard_stats_card.dart';
import '../widgets/recent_activities_widget.dart';

class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  final _adminAuthController = Get.find<AdminAuthController>();
  final _userManagementController = Get.put(UserManagementController());
  final _walletController = Get.put(WalletManagementController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AdminTheme.backgroundColor,
      body: Row(
        children: [
          // Sidebar
          const AdminSidebar(),

          // Main Content
          Expanded(
            child: Column(
              children: [
                // Top App Bar
                _buildTopAppBar(),

                // Dashboard Content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Welcome Section
                        _buildWelcomeSection(),
                        const SizedBox(height: 24),

                        // Stats Cards
                        _buildStatsSection(),
                        const SizedBox(height: 24),

                        // Recent Activities
                        _buildRecentActivitiesSection(),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopAppBar() {
    return Container(
      height: 70,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: Row(
          children: [
            Text(
              'Dashboard',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
            ),
            const Spacer(),

            // Notifications
            IconButton(
              icon: const Icon(Icons.notifications_outlined),
              onPressed: () {
                Get.snackbar('Info',
                    'Notifications feature will be available in the next update');
              },
            ),
            const SizedBox(width: 16),

            // Admin Profile
            Obx(() => PopupMenuButton<String>(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CircleAvatar(
                        backgroundColor: Colors.deepPurple,
                        child: Text(
                          _adminAuthController.currentAdmin?.name
                                  .substring(0, 1)
                                  .toUpperCase() ??
                              'A',
                          style: const TextStyle(
                              color: Colors.white, fontWeight: FontWeight.bold),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _adminAuthController.currentAdmin?.name ?? 'Admin',
                            style: const TextStyle(fontWeight: FontWeight.w600),
                          ),
                          Text(
                            _adminAuthController.currentAdmin?.role.name
                                    .toUpperCase() ??
                                '',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(width: 4),
                      const Icon(Icons.keyboard_arrow_down),
                    ],
                  ),
                  onSelected: (value) {
                    if (value == 'logout') {
                      _handleLogout();
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'logout',
                      child: ListTile(
                        leading: Icon(Icons.logout, color: Colors.red),
                        title:
                            Text('Logout', style: TextStyle(color: Colors.red)),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ],
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Obx(() => Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.deepPurple, Colors.deepPurple.shade300],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome back, ${_adminAuthController.currentAdmin?.name ?? 'Admin'}!',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Here\'s what\'s happening with Money Mouthy today.',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(
                Icons.dashboard,
                color: Colors.white,
                size: 48,
              ),
            ],
          ),
        ));
  }

  Widget _buildStatsSection() {
    return Obx(() => Row(
          children: [
            Expanded(
              child: DashboardStatsCard(
                title: 'Total Users',
                value: _userManagementController.totalUsers.toString(),
                icon: Icons.people,
                color: AdminTheme.primaryColor,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DashboardStatsCard(
                title: 'Active Users',
                value: _userManagementController.activeUsers.toString(),
                icon: Icons.person,
                color: AdminTheme.successColor,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DashboardStatsCard(
                title: 'Blocked Users',
                value: _userManagementController.blockedUsers.toString(),
                icon: Icons.block,
                color: AdminTheme.errorColor,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DashboardStatsCard(
                title: 'Total Balance',
                value: '\$${_walletController.totalBalance.toStringAsFixed(2)}',
                icon: Icons.attach_money,
                color: Colors.orange,
              ),
            ),
          ],
        ));
  }

  Widget _buildRecentActivitiesSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: const RecentActivitiesWidget(),
    );
  }

  void _handleLogout() {
    Get.dialog(
      AlertDialog(
        title: const Text('Confirm Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _adminAuthController.signOut();
              Get.offAllNamed('/admin/login');
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}
