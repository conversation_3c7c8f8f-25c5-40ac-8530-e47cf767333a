import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:money_mouthy_two/controllers/post_controller.dart';
import 'package:money_mouthy_two/widgets/top_paid_post_container.dart';
import 'package:money_mouthy_two/screens/post_detail_screen.dart';
import 'package:money_mouthy_two/utils/debouncer.dart';
import 'posts_feed.dart';

/// Explore Tab Widget
class ExploreTab extends StatefulWidget {
  final String selectedCategory;

  const ExploreTab({super.key, required this.selectedCategory});

  @override
  State<ExploreTab> createState() => _ExploreTabState();
}

class _ExploreTabState extends State<ExploreTab> with DebounceMixin {
  @override
  void initState() {
    super.initState();
    // PostController is already initialized in main.dart
  }

  @override
  void dispose() {
    disposeAllDebouncers();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    bool isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    Widget content = Column(
      children: [
        // Category Header
        Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: Center(
            child: Text(
              widget.selectedCategory,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ),
        ),
        // Top Paid Post Container (48-hour system) - Optimized with caching
        // Check if PostController is both registered AND initialized
        if (!Get.isRegistered<PostController>())
          const SizedBox.shrink()
        else
          Obx(
            () {
              final controller = Get.find<PostController>();

              // Don't show top post if controller is not initialized yet
              if (!controller.isInitialized) {
                return const SizedBox.shrink();
              }

              // Get the current top post for this category
              final topPost = controller.getTopPaidPostForCategory(
                widget.selectedCategory,
              );

              if (topPost == null) {
                return const SizedBox.shrink();
              }

              return LayoutBuilder(
                builder: (context, constraints) {
                  return TopPaidPostContainer(
                    category: widget.selectedCategory,
                    topPost: topPost,
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => PostDetailScreen(post: topPost),
                        ),
                      );
                    },
                  );
                },
              );
            },
          ),
        Expanded(
          child: PostsFeed(
            key: ValueKey(widget.selectedCategory),
            category: widget.selectedCategory,
          ),
        ),
      ],
    );

    return isLandscape
        ? SingleChildScrollView(
            child: SizedBox(
              height: MediaQuery.of(context).size.height -
                  MediaQuery.of(context).padding.top -
                  kToolbarHeight -
                  kBottomNavigationBarHeight -
                  48, // Tab bar height
              child: content,
            ),
          )
        : content;
  }
}
