# Money Mouthy Admin Panel - Setup Guide

## 🚀 Production Setup Instructions

### Prerequisites

- Flutter project with Firebase configured
- Firebase Authentication enabled
- Firestore database set up
- Admin panel code integrated into your project

### Step 1: Firebase Security Rules

Ensure your `firestore.rules` file includes the admin security rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if user is verified
    function isVerified() {
      return request.auth != null && request.auth.token.email_verified;
    }

    // Admin collections - only accessible by verified admins
    match /admins/{adminId} {
      allow read, write: if request.auth != null &&
                         request.auth.uid == adminId &&
                         isVerified();
    }

    match /admin_logs/{logId} {
      allow read, write: if request.auth != null &&
                         exists(/databases/$(database)/documents/admins/$(request.auth.uid)) &&
                         isVerified();
    }

    match /user_activities/{activityId} {
      allow read, write: if request.auth != null &&
                         exists(/databases/$(database)/documents/admins/$(request.auth.uid)) &&
                         isVerified();
    }

    // Regular user collections
    match /users/{userId} {
      allow read, write: if request.auth != null &&
                         (request.auth.uid == userId ||
                          exists(/databases/$(database)/documents/admins/$(request.auth.uid))) &&
                         isVerified();
    }

    match /wallets/{userId} {
      allow read, write: if request.auth != null &&
                         (request.auth.uid == userId ||
                          exists(/databases/$(database)/documents/admins/$(request.auth.uid))) &&
                         isVerified();
    }

    // Other collections
    match /{document=**} {
      allow read, write: if isVerified();
    }
  }
}
```

### Step 2: Create Your First Admin Account

#### Option A: Using Firebase Console (Recommended for Production)

1. **Create Firebase Auth User:**

   - Go to Firebase Console → Authentication → Users
   - Click "Add user"
   - Enter admin email and secure password
   - Copy the User UID

2. **Create Admin Document:**
   - Go to Firestore Database
   - Create collection: `admins`
   - Create document with the User UID as document ID
   - Add the following fields:

```json
{
  "email": "<EMAIL>",
  "name": "System Administrator",
  "role": "superAdmin",
  "status": "active",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "permissions": [
    "manage_users",
    "manage_wallets",
    "view_analytics",
    "manage_admins",
    "system_admin"
  ]
}
```

#### Option B: Using Code (Development Only)

```dart
import 'package:money_mouthy_two/admin/utils/admin_setup.dart';

// Create super admin
await AdminSetup.createSuperAdmin(
  email: '<EMAIL>',
  password: 'YourSecurePassword123!',
  name: 'System Administrator',
);
```

### Step 3: Access the Admin Panel

1. **Navigate to Admin Login:**

   - Web: `https://yourdomain.com/admin/login`
   - Development: `http://localhost:3000/admin/login`

2. **Login with Admin Credentials:**

   - Email: Your admin email
   - Password: Your admin password

3. **Verify Access:**
   - You should be redirected to `/admin/dashboard`
   - Check that all menu items are accessible based on permissions

### Step 4: Configure Admin Permissions

#### Available Permissions:

- `manage_users`: Create, edit, block/unblock user accounts
- `manage_wallets`: View and manage user wallets and transactions
- `view_analytics`: Access analytics and reporting features
- `manage_admins`: Create and manage other admin accounts
- `system_admin`: Full system administration access

#### Admin Roles:

1. **Super Admin**: All permissions, can manage other admins
2. **Admin**: User and wallet management, analytics access
3. **Moderator**: Limited user management and analytics

### Step 5: Create Additional Admin Users

1. **From Admin Panel:**

   - Login as Super Admin
   - Navigate to Admin Management (if implemented)
   - Create new admin accounts with appropriate roles

2. **Using Code:**

```dart
await AdminSetup.createAdmin(
  email: '<EMAIL>',
  password: 'SecurePassword123!',
  name: 'Content Moderator',
  role: AdminRole.moderator,
  permissions: ['manage_users', 'view_analytics'],
);
```

### Step 6: Security Best Practices

#### Password Requirements:

- Minimum 8 characters
- Include uppercase, lowercase, numbers, and special characters
- Use unique passwords for each admin account

#### Account Security:

- Enable 2FA on Firebase accounts
- Regularly rotate admin passwords
- Monitor admin activity logs
- Use strong, unique passwords
- Limit admin account creation to trusted personnel

#### Network Security:

- Use HTTPS in production
- Consider IP whitelisting for admin access
- Implement rate limiting on login attempts
- Monitor for suspicious login patterns

### Step 7: Monitoring and Maintenance

#### Regular Tasks:

- Review admin activity logs weekly
- Update admin permissions as needed
- Remove inactive admin accounts
- Monitor system performance
- Backup admin configurations

#### Audit Trail:

All admin actions are automatically logged with:

- Admin ID and name
- Action performed
- Timestamp
- Target user/resource
- Additional metadata

### Troubleshooting

#### Common Issues:

**"Admin access denied" error:**

- Verify admin document exists in Firestore
- Check admin status is "active"
- Ensure Firebase rules are deployed

**Permission denied errors:**

- Verify admin has required permissions
- Check Firebase security rules
- Ensure admin role is correctly set

**Login failures:**

- Check email/password combination
- Verify Firebase Auth is enabled
- Check browser console for errors

#### Support:

- Check Firebase Console for authentication errors
- Review Firestore security rules
- Monitor browser developer tools for client-side errors
- Check Flutter logs for server-side issues

### Production Deployment Checklist

- [ ] Firebase security rules deployed
- [ ] Super admin account created
- [ ] Admin panel routes configured
- [ ] HTTPS enabled
- [ ] Admin credentials documented securely
- [ ] Backup procedures established
- [ ] Monitoring systems in place
- [ ] Team training completed

## 🔐 Default Credentials (Development Only)

**⚠️ WARNING: Only use these for development. Change immediately in production!**

If you run the development setup:

```dart
await AdminSetup.setupDevelopmentAdmins();
```

You'll get these accounts:

- **Super Admin**: `<EMAIL>` / `SuperAdmin123!`
- **Admin**: `<EMAIL>` / `Admin123!`
- **Moderator**: `<EMAIL>` / `Moderator123!`

**🚨 IMPORTANT: Delete these accounts before going to production!**

## 📞 Support

For technical support:

1. Check this documentation first
2. Review Firebase Console for errors
3. Check browser developer tools
4. Contact your development team

---

**Security Notice**: Keep this guide secure and limit access to authorized personnel only. Admin credentials should never be shared or stored in version control.
