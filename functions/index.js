const functions = require("firebase-functions");
const stripe = require("stripe")(functions.config().stripe.secret_key);
const cors = require("cors")({ origin: true });
const admin = require("firebase-admin");

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

exports.createPaymentIntent = functions.https.onRequest(async (req, res) => {
  // Handle CORS preflight requests
  return cors(req, res, async () => {
    try {
      // Set additional CORS headers
      res.set({
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
        "Access-Control-Allow-Headers":
          "Content-Type, Authorization, X-Requested-With, Accept, Origin",
        "Access-Control-Max-Age": "86400", // 24 hours
        "Content-Type": "application/json",
      });

      // Handle OPTIONS preflight request
      if (req.method === "OPTIONS") {
        return res.status(200).send();
      }

      // Only allow POST requests for creating payment intents
      if (req.method !== "POST") {
        return res.status(405).json({
          error: "Method not allowed. Only POST requests are accepted.",
          success: false,
        });
      }

      // Validate request body
      const { amount, currency = "usd", email } = req.body || {};

      if (!amount || !email) {
        return res.status(400).json({
          error: "Missing required fields: amount and email are required.",
          success: false,
        });
      }
      if (typeof amount !== "number") {
        //  Parse
        amount = parseFloat(amount);
        if (amount <= 0) {
          return res.status(400).json({
            error: "Amount must be a positive number.",
            success: false,
          });
        }
      }
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({
          error: "Invalid email format.",
          success: false,
        });
      }

      // Find or create customer
      let customer;
      try {
        const customers = await stripe.customers.list({ email, limit: 1 });
        customer = customers.data[0]
          ? customers.data[0]
          : await stripe.customers.create({ email });
      } catch (stripeError) {
        console.error("Error handling customer:", stripeError);
        return res.status(500).json({
          error: "Failed to create or retrieve customer.",
          success: false,
        });
      }

      // Create ephemeral key
      let ephemeralKey;
      try {
        ephemeralKey = await stripe.ephemeralKeys.create(
          { customer: customer.id },
          { apiVersion: "2020-08-27" }
        );
      } catch (stripeError) {
        console.error("Error creating ephemeral key:", stripeError);
        return res.status(500).json({
          error: "Failed to create ephemeral key.",
          success: false,
        });
      }

      // Create payment intent
      let paymentIntent;
      try {
        paymentIntent = await stripe.paymentIntents.create({
          amount: Math.round(amount), // Ensure amount is an integer
          currency,
          customer: customer.id,
          automatic_payment_methods: {
            enabled: true,
          },
        });
      } catch (stripeError) {
        console.error("Error creating payment intent:", stripeError);
        return res.status(500).json({
          error: "Failed to create payment intent.",
          success: false,
        });
      }

      // Return successful response
      return res.status(200).json({
        paymentIntent: paymentIntent.client_secret,
        ephemeralKey: ephemeralKey.secret,
        customer: customer.id,
        customerEmail: customer.email,
        success: true,
      });
    } catch (error) {
      console.error("Unexpected error creating payment intent:", error);
      return res.status(500).json({
        error: "An unexpected error occurred. Please try again.",
        success: false,
      });
    }
  });
});

// Handle payment completion and update wallet
exports.handlePaymentCompletion = functions.https.onRequest(
  async (req, res) => {
    return cors(req, res, async () => {
      try {
        // Set CORS headers
        res.set({
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
          "Access-Control-Allow-Headers":
            "Content-Type, Authorization, X-Requested-With, Accept, Origin",
          "Access-Control-Max-Age": "86400",
          "Content-Type": "application/json",
        });

        if (req.method === "OPTIONS") {
          return res.status(200).send();
        }

        if (req.method !== "POST") {
          return res.status(405).json({
            error: "Method not allowed. Only POST requests are accepted.",
            success: false,
          });
        }

        const { paymentIntentId, userId } = req.body || {};

        if (!paymentIntentId || !userId) {
          return res.status(400).json({
            error:
              "Missing required fields: paymentIntentId and userId are required.",
            success: false,
          });
        }

        // Retrieve payment intent from Stripe
        const paymentIntent = await stripe.paymentIntents.retrieve(
          paymentIntentId
        );

        if (paymentIntent.status !== "succeeded") {
          return res.status(400).json({
            error: `Payment not completed. Status: ${paymentIntent.status}`,
            success: false,
          });
        }

        // Calculate amount in dollars (Stripe uses cents)
        const amount = paymentIntent.amount / 100;

        // Get Firestore references
        const db = admin.firestore();
        const walletRef = db.collection("wallets").doc(userId);
        const transactionsRef = walletRef.collection("transactions");

        // Check if this payment has already been processed
        const existingTransaction = await transactionsRef
          .where("externalTransactionId", "==", paymentIntentId)
          .limit(1)
          .get();

        if (!existingTransaction.empty) {
          return res.status(200).json({
            message: "Payment already processed",
            success: true,
            alreadyProcessed: true,
          });
        }

        // Use a transaction to ensure atomicity
        await db.runTransaction(async (transaction) => {
          // Get current wallet data
          const walletDoc = await transaction.get(walletRef);
          const currentBalance = walletDoc.exists
            ? walletDoc.data().balance || 0
            : 0;
          const newBalance = currentBalance + amount;

          // Create transaction record matching the app's schema (except timestamp)
          const transactionData = {
            type: "credit",
            amount: amount,
            description: `Wallet funding via Stripe - $${amount.toFixed(2)}`,
            timestamp: Date.now(), // Use milliseconds since epoch to match app schema
            status: "completed",
            postId: null,
            paymentMethodId: paymentIntent.payment_method,
            externalTransactionId: paymentIntentId,
            metadata: {
              paymentIntentId: paymentIntentId,
              stripePaymentMethod: paymentIntent.payment_method,
              processedBy: "firebase-function",
            },
          };

          // Add transaction
          const newTransactionRef = transactionsRef.doc();
          transaction.set(newTransactionRef, transactionData);

          // Update wallet balance
          transaction.set(
            walletRef,
            {
              balance: newBalance,
              lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
            },
            { merge: true }
          );
        });

        return res.status(200).json({
          message: "Payment processed successfully",
          success: true,
          amount: amount,
          newBalance: null, // We don't return the balance for security
        });
      } catch (error) {
        console.error("Error processing payment completion:", error);
        return res.status(500).json({
          error: "Failed to process payment completion",
          success: false,
        });
      }
    });
  }
);

/**
 * Helper function to delete media files from Firebase Storage
 */
async function deleteMediaFiles(imageUrls, videoUrls) {
  const storage = admin.storage();
  const bucket = storage.bucket();

  let deletedFiles = 0;
  const errors = [];

  // Delete image files
  if (imageUrls && imageUrls.length > 0) {
    for (const imageUrl of imageUrls) {
      try {
        // Extract file path from URL
        const filePath = extractFilePathFromUrl(imageUrl);
        if (filePath) {
          await bucket.file(filePath).delete();
          deletedFiles++;
          console.log(`Deleted image: ${filePath}`);
        }
      } catch (error) {
        console.error(`Failed to delete image ${imageUrl}:`, error.message);
        errors.push(`Image: ${error.message}`);
      }
    }
  }

  // Delete video files
  if (videoUrls && videoUrls.length > 0) {
    for (const videoUrl of videoUrls) {
      try {
        // Extract file path from URL
        const filePath = extractFilePathFromUrl(videoUrl);
        if (filePath) {
          await bucket.file(filePath).delete();
          deletedFiles++;
          console.log(`Deleted video: ${filePath}`);
        }
      } catch (error) {
        console.error(`Failed to delete video ${videoUrl}:`, error.message);
        errors.push(`Video: ${error.message}`);
      }
    }
  }

  return { deletedFiles, errors };
}

/**
 * Helper function to extract file path from Firebase Storage URL
 */
function extractFilePathFromUrl(url) {
  try {
    // Handle both download URLs and gs:// URLs
    if (url.includes("firebasestorage.googleapis.com")) {
      // Extract from download URL
      const match = url.match(/\/o\/(.+?)\?/);
      return match ? decodeURIComponent(match[1]) : null;
    } else if (url.startsWith("gs://")) {
      // Extract from gs:// URL
      const match = url.match(/gs:\/\/[^\/]+\/(.+)/);
      return match ? match[1] : null;
    }
    return null;
  } catch (error) {
    console.error("Error extracting file path from URL:", error);
    return null;
  }
}

/**
 * Core cleanup logic that can be used by both scheduled and manual functions
 */
async function performPostCleanup(batchSize = 500) {
  const db = admin.firestore();
  const now = new Date();

  // Calculate 24 hours ago
  const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  const cutoffTimestamp =
    admin.firestore.Timestamp.fromDate(twentyFourHoursAgo);

  console.log(`Deleting posts older than: ${twentyFourHoursAgo.toISOString()}`);

  // Query posts older than 24 hours
  const oldPostsQuery = db
    .collection("posts")
    .where("createdAt", "<", cutoffTimestamp)
    .limit(batchSize);

  const snapshot = await oldPostsQuery.get();

  if (snapshot.empty) {
    console.log("No old posts found to delete");
    return {
      success: true,
      deletedCount: 0,
      deletedMediaFiles: 0,
      cutoffTime: twentyFourHoursAgo.toISOString(),
      mediaErrors: [],
    };
  }

  console.log(`Found ${snapshot.size} posts to delete`);

  // Collect media files to delete and prepare batch deletion
  const batch = db.batch();
  let deleteCount = 0;
  let totalDeletedMediaFiles = 0;
  const allMediaErrors = [];

  for (const doc of snapshot.docs) {
    const postData = doc.data();

    // Delete associated media files
    const { deletedFiles, errors } = await deleteMediaFiles(
      postData.imageUrls || (postData.imageUrl ? [postData.imageUrl] : []),
      postData.videoUrls || []
    );

    totalDeletedMediaFiles += deletedFiles;
    allMediaErrors.push(...errors);

    // Queue post for deletion
    batch.delete(doc.ref);
    deleteCount++;

    console.log(
      `Queued for deletion: Post ${doc.id} from ${postData.createdAt
        ?.toDate()
        ?.toISOString()} (${deletedFiles} media files deleted)`
    );
  }

  // Execute batch deletion of posts
  await batch.commit();

  console.log(
    `Successfully deleted ${deleteCount} old posts and ${totalDeletedMediaFiles} media files`
  );
  if (allMediaErrors.length > 0) {
    console.warn(`Media deletion errors: ${allMediaErrors.join(", ")}`);
  }

  // Log statistics
  const remainingPostsSnapshot = await db.collection("posts").get();
  console.log(`Remaining posts in database: ${remainingPostsSnapshot.size}`);

  return {
    success: true,
    deletedCount: deleteCount,
    deletedMediaFiles: totalDeletedMediaFiles,
    remainingCount: remainingPostsSnapshot.size,
    cutoffTime: twentyFourHoursAgo.toISOString(),
    mediaErrors: allMediaErrors,
  };
}

/**
 * Cloud Function to automatically delete posts older than 24 hours
 * Runs every hour using Cloud Scheduler
 */
exports.cleanupOldPosts = functions.pubsub
  // Every Hour
  .schedule("0 * * * *") // Run every hour
  .onRun(async () => {
    console.log("Starting scheduled cleanup of old posts...");

    try {
      const result = await performPostCleanup(500);
      console.log("Scheduled cleanup completed:", result);
      return result;
    } catch (error) {
      console.error("Error during scheduled post cleanup:", error);

      // Don't throw error to prevent function retries
      // Just log and return error status
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  });

/**
 * Manual cleanup function for testing and emergency cleanup
 * HTTP trigger that can be called manually
 */
exports.manualCleanupOldPosts = functions.https.onRequest(async (req, res) => {
  return cors(req, res, async () => {
    try {
      // Set CORS headers
      res.set({
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
        "Access-Control-Allow-Headers":
          "Content-Type, Authorization, X-Requested-With, Accept, Origin",
        "Access-Control-Max-Age": "86400",
        "Content-Type": "application/json",
      });

      if (req.method === "OPTIONS") {
        return res.status(200).send();
      }

      console.log("Starting manual cleanup of old posts...");

      // Allow custom batch size from query parameter
      const batchSize = parseInt(req.query.batchSize) || 500;

      if (batchSize > 1000) {
        return res.status(400).json({
          error: "Batch size cannot exceed 1000",
          success: false,
        });
      }

      const result = await performPostCleanup(batchSize);

      console.log("Manual cleanup completed:", result);

      return res.status(200).json({
        message: "Manual cleanup completed successfully",
        ...result,
      });
    } catch (error) {
      console.error("Error during manual post cleanup:", error);
      return res.status(500).json({
        error: "Failed to perform manual cleanup",
        details: error.message,
        success: false,
      });
    }
  });
});

/**
 * Get cleanup statistics and information
 * HTTP trigger that provides insights into post cleanup status
 */
exports.getCleanupStats = functions.https.onRequest(async (req, res) => {
  return cors(req, res, async () => {
    try {
      // Set CORS headers
      res.set({
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
        "Access-Control-Allow-Headers":
          "Content-Type, Authorization, X-Requested-With, Accept, Origin",
        "Access-Control-Max-Age": "86400",
        "Content-Type": "application/json",
      });

      if (req.method === "OPTIONS") {
        return res.status(200).send();
      }

      console.log("Fetching cleanup statistics...");

      const db = admin.firestore();
      const now = new Date();

      // Calculate time thresholds
      const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const fortyEightHoursAgo = new Date(now.getTime() - 48 * 60 * 60 * 1000);

      const cutoff24h = admin.firestore.Timestamp.fromDate(twentyFourHoursAgo);
      const cutoff48h = admin.firestore.Timestamp.fromDate(fortyEightHoursAgo);

      // Get total posts
      const totalPostsSnapshot = await db.collection("posts").get();
      const totalPosts = totalPostsSnapshot.size;

      // Get posts from last 24 hours
      const postsLast24hSnapshot = await db
        .collection("posts")
        .where("createdAt", ">=", cutoff24h)
        .get();
      const postsLast24Hours = postsLast24hSnapshot.size;

      // Get posts from last 48 hours
      const postsLast48hSnapshot = await db
        .collection("posts")
        .where("createdAt", ">=", cutoff48h)
        .get();
      const postsLast48Hours = postsLast48hSnapshot.size;

      // Get posts older than 24 hours (eligible for deletion)
      const postsToDeleteSnapshot = await db
        .collection("posts")
        .where("createdAt", "<", cutoff24h)
        .get();
      const postsToDelete = postsToDeleteSnapshot.size;

      const stats = {
        totalPosts,
        postsLast24Hours,
        postsLast48Hours,
        postsOlderThan24Hours: postsToDelete,
        nextCleanupWillDelete: postsToDelete,
        cutoffTime: twentyFourHoursAgo.toISOString(),
        timestamp: new Date().toISOString(),
        success: true,
      };

      console.log("Cleanup statistics:", stats);

      return res.status(200).json(stats);
    } catch (error) {
      console.error("Error fetching cleanup statistics:", error);
      return res.status(500).json({
        error: "Failed to fetch cleanup statistics",
        details: error.message,
        success: false,
      });
    }
  });
});
